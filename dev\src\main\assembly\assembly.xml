<?xml version="1.0" encoding="UTF-8"?>
<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<id>dist</id>

	<!-- Generates a zip package containing the needed files -->
	<formats>
		<format>dir</format>
	</formats>

	<!-- Adds dependencies to zip package under lib directory -->
	<dependencySets>
		<dependencySet>
			<!-- Project artifact is not copied under library directory since it is 
				added to the root directory of the zip package. -->
			<scope>runtime</scope>
			<useProjectArtifact>false</useProjectArtifact>
			<outputDirectory>lib</outputDirectory>
			<unpack>false</unpack>
		</dependencySet>
	</dependencySets>

	<fileSets>
		<!-- Adds startup scripts to the root directory of zip package. The startup 
			scripts are located to src/main/scripts directory as stated by Maven conventions. -->
		<fileSet>
			<directory>${project.build.scriptSourceDirectory}</directory>
			<outputDirectory>/</outputDirectory>
			<includes>
				<include>startup.*</include>
			</includes>
		</fileSet>
		<!-- adds jar package to the root directory of zip package -->
		<fileSet>
			<directory>${project.build.directory}</directory>
			<outputDirectory></outputDirectory>
			<includes>
				<include>*.jar</include>
			</includes>
		</fileSet>
		<!-- adding config -->
		<fileSet>
			<directory>${basedir}/src/main/resources</directory>
			<outputDirectory>/</outputDirectory>
			<includes>
				<include>*.xml</include>
				<include>*.properties</include>
			</includes>
		</fileSet>
	</fileSets>
</assembly>