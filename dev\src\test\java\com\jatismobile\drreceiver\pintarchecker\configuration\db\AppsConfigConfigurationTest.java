package com.jatismobile.drreceiver.pintarchecker.configuration.db;

import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.PlatformTransactionManager;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AppsConfigConfigurationTest {

    @InjectMocks
    private AppsConfigConfiguration appsConfigConfiguration;

    @Mock
    private DataSourceProperties dataSourceProperties;

    @Mock
    private EntityManagerFactoryBuilder entityManagerFactoryBuilder;

    @Mock
    private EntityManagerFactory entityManagerFactory;

    @Mock
    private DataSource dataSource;

    @Mock
    private HikariDataSource hikariDataSource;

    @Mock
    private ApplicationReadyEvent applicationReadyEvent;

    @Mock
    private EntityManagerFactoryBuilder.Builder builder;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(appsConfigConfiguration, "appName", "TestApp");
    }

    @Test
    void testStartUp(){
        try (MockedStatic<AppLogUtil> mockAppLogUtil = Mockito.mockStatic(AppLogUtil.class)) {
            
            appsConfigConfiguration.onStartup(null);
            
            mockAppLogUtil.verify(() -> AppLogUtil.writeInfoLog(null, "[TestApp] | AppsConfigConfiguration is ready."), times(1));
        }
    }

    @Test
    void testCustomDataSourceProperties() {
        DataSourceProperties result = appsConfigConfiguration.customDataSourceProperties();
        assertNotNull(result);
    }

    @Test
    void testCustomEntityManagerFactory() {
        when(entityManagerFactoryBuilder.dataSource(any(DataSource.class))).thenReturn(builder);
        when(builder.packages(anyString())).thenReturn(builder);
        when(builder.build()).thenReturn(new LocalContainerEntityManagerFactoryBean());

        LocalContainerEntityManagerFactoryBean result = appsConfigConfiguration.customEntityManagerFactory(
                entityManagerFactoryBuilder, dataSource);
        
        assertNotNull(result);
        verify(entityManagerFactoryBuilder).dataSource(dataSource);
        verify(builder).packages("com.jatismobile.transmitter.fastsignal.entity.mysql.appsconfig");
        verify(builder).build();
    }

    @Test
    void testCustomTransactionManager() {
        PlatformTransactionManager result = appsConfigConfiguration.customTransactionManager(entityManagerFactory);
        assertNotNull(result);
    }

    @Test
    void testCustomDataSourceSuccess() {
        DataSourceProperties dataSourcePropertiesLocal = mock(DataSourceProperties.class, RETURNS_DEEP_STUBS);
        when(dataSourcePropertiesLocal.initializeDataSourceBuilder().type(HikariDataSource.class).build()).thenReturn(hikariDataSource);

        HikariDataSource result = appsConfigConfiguration.customDataSource(dataSourcePropertiesLocal);
        
        assertNotNull(result);
        assertEquals(hikariDataSource, result);
        verify(dataSourcePropertiesLocal.initializeDataSourceBuilder().type(HikariDataSource.class), times(1)).build();
    }
}
