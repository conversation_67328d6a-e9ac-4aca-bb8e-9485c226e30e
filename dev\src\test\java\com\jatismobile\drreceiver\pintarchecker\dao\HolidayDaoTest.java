//package com.jatismobile.drreceiver.pintarchecker.dao;
//
//import static org.junit.jupiter.api.Assertions.assertFalse;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.anyInt;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.mockStatic;
//import static org.mockito.Mockito.when;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//
//class HolidayDaoTest {
//
//    private static final String URL_DB = "**********************************";
//    private static final String USER_DB = "testuser";
//    private static final String PASS_DB = "testpass";
//    private static final String TABLE = "holidays";
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void testIsHolidayWhenHolidayExistsReturnsTrue() throws Exception {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        ResultSet mockResultSet = mock(ResultSet.class);
//
//        try (MockedStatic<DriverManager> driverManager = mockStatic(DriverManager.class)) {
//            driverManager.when(() -> DriverManager.getConnection(URL_DB, USER_DB, PASS_DB)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
//            when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
//            when(mockResultSet.next()).thenReturn(true);
//            when(mockResultSet.getString("name")).thenReturn("New Year");
//
//            boolean result = HolidayDao.isHoliday(URL_DB, USER_DB, PASS_DB, TABLE, "2024-01-01");
//            assertTrue(result);
//        }
//    }
//
//    @Test
//    void testIsHolidayWhenNoHolidayReturnsFalse() throws Exception {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        ResultSet mockResultSet = mock(ResultSet.class);
//
//        try (MockedStatic<DriverManager> driverManager = mockStatic(DriverManager.class)) {
//            driverManager.when(() -> DriverManager.getConnection(URL_DB, USER_DB, PASS_DB)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
//            when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
//            when(mockResultSet.next()).thenReturn(false);
//
//            boolean result = HolidayDao.isHoliday(URL_DB, USER_DB, PASS_DB, TABLE, "2024-01-02");
//            assertFalse(result);
//        }
//    }
//
//    @Test
//    void testIsHolidayWhenSQLExceptionReturnsFalse() throws Exception {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        try (MockedStatic<DriverManager> driverManager = mockStatic(DriverManager.class)) {
//            driverManager.when(() -> DriverManager.getConnection(URL_DB, USER_DB, PASS_DB)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
//            Mockito.doThrow(new SQLException("Database connection failed")).when(mockPreparedStatement).setString(anyInt(), anyString());
//
//            boolean result = HolidayDao.isHoliday(URL_DB, USER_DB, PASS_DB, TABLE, "2024-01-01");
//            assertFalse(result);
//        }
//    }
//
//    @Test
//    void testIsHolidayWithInvalidTableReturnsFalse() throws Exception {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        try (MockedStatic<DriverManager> driverManager = mockStatic(DriverManager.class)) {
//            driverManager.when(() -> DriverManager.getConnection(URL_DB, USER_DB, PASS_DB)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
//            Mockito.doThrow(new SQLException("Invalid table name")).when(mockPreparedStatement).setString(anyInt(), anyString());
//
//            boolean result = HolidayDao.isHoliday(URL_DB, USER_DB, PASS_DB, "invalid_table", "2024-01-01");
//            assertFalse(result);
//        }
//    }
//}