package com.jatismobile.drreceiver.pintarchecker.dao;

import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;

public class RerouteMappingDao {

	private RerouteMappingDao() {}

    public static Map<String, String> findAll(String urldb, String userdb, String passdb, String table){
        Map<String, String> rerouteMap = new HashMap<>();

        String query = String.format("select cr.provider_id, p.name as provider_name, co.name as origin_channel_name, co.mq_name as origin_mq_name, cd.name as reroute_channel_name, cd.mq_name as reroute_mq_name from %s cr join providers p on cr.provider_id = p.id left join channels co on cr.origin_channel_id = co.id left join channels cd on cr.reroute_channel_id = cd.id order by p.id", table);


        try (Connection con = DriverManager.getConnection(urldb, userdb, passdb);
             PreparedStatement st = con.prepareStatement(query);
             ResultSet rs = st.executeQuery()) {

            AppLogUtil.writeInfoLog(null, "[DB] Fetching data from table: " + table + " with query: " + query);

            while (rs.next()) {
                String id = rs.getString("provider_id");
                String originChannelName = rs.getString("origin_mq_name");
                String rerouteChannelName = rs.getString("reroute_mq_name");
                rerouteMap.put(id+"-"+originChannelName, rerouteChannelName);
            }

            AppLogUtil.writeInfoLog(null, "[DB] Successfully retrieved config rerouteMapping");

        } catch (SQLException e) {
            AppLogUtil.writeErrorLog(null, "[DB] Failed to fetch config rerouteMapping", e);
        }

        return rerouteMap;
    }

}
