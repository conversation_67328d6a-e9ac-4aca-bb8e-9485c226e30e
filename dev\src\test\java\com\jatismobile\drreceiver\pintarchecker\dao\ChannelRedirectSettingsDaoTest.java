//package com.jatismobile.drreceiver.pintarchecker.dao;
//
//import static org.junit.jupiter.api.Assertions.*;
//import org.junit.jupiter.api.Test;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//
//class ChannelRedirectSettingsDaoTest {
//
//    private static final String TEST_URL = "**********************************";
//    private static final String TEST_USER = "testuser";
//    private static final String TEST_PASS = "testpass";
//    private static final String TEST_MQ_NAME = "test.queue";
//
//    @Test
//    void testGetTargetMqNameSuccess() throws Exception {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//            PreparedStatement mockPreparedStatement = Mockito.mock(PreparedStatement.class);
//            ResultSet mockResultSet = Mockito.mock(ResultSet.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenReturn(mockPreparedStatement);
//            Mockito.when(mockPreparedStatement.executeQuery())
//                .thenReturn(mockResultSet);
//            Mockito.when(mockResultSet.next()).thenReturn(true, false);
//            Mockito.when(mockResultSet.getString("target_mq_name"))
//                .thenReturn("target.queue");
//
//            String result = ChannelRedirectSettingsDao.getTargetMqName(
//                TEST_URL, TEST_USER, TEST_PASS, TEST_MQ_NAME);
//
//            assertEquals("target.queue", result);
//        }
//    }
//
//    @Test
//    void testGetTargetMqNameNoResults() throws Exception {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//            PreparedStatement mockPreparedStatement = Mockito.mock(PreparedStatement.class);
//            ResultSet mockResultSet = Mockito.mock(ResultSet.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenReturn(mockPreparedStatement);
//            Mockito.when(mockPreparedStatement.executeQuery())
//                .thenReturn(mockResultSet);
//            Mockito.when(mockResultSet.next()).thenReturn(false);
//
//            String result = ChannelRedirectSettingsDao.getTargetMqName(
//                TEST_URL, TEST_USER, TEST_PASS, TEST_MQ_NAME);
//
//            assertEquals("", result);
//        }
//    }
//
//    @Test
//    void testGetOriginListSuccess() throws Exception {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//            PreparedStatement mockPreparedStatement = Mockito.mock(PreparedStatement.class);
//            ResultSet mockResultSet = Mockito.mock(ResultSet.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenReturn(mockPreparedStatement);
//            Mockito.when(mockPreparedStatement.executeQuery())
//                .thenReturn(mockResultSet);
//            Mockito.when(mockResultSet.next()).thenReturn(true, true, false);
//            Mockito.when(mockResultSet.getString("origin_mq_name"))
//                .thenReturn("origin1.queue", "origin2.queue");
//
//            String result = ChannelRedirectSettingsDao.getOriginList(
//                TEST_URL, TEST_USER, TEST_PASS, TEST_MQ_NAME);
//
//            assertEquals("origin1.queue|origin2.queue|", result);
//        }
//    }
//
//    @Test
//    void testGetOriginListNoResults() throws Exception {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//            PreparedStatement mockPreparedStatement = Mockito.mock(PreparedStatement.class);
//            ResultSet mockResultSet = Mockito.mock(ResultSet.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenReturn(mockPreparedStatement);
//            Mockito.when(mockPreparedStatement.executeQuery())
//                .thenReturn(mockResultSet);
//            Mockito.when(mockResultSet.next()).thenReturn(false);
//
//            String result = ChannelRedirectSettingsDao.getOriginList(
//                TEST_URL, TEST_USER, TEST_PASS, TEST_MQ_NAME);
//
//            assertEquals("", result);
//        }
//    }
//
//    @Test
//    void testGetTargetMqNameDatabaseError() throws Exception {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenThrow(new RuntimeException("Database error"));
//
//            String result = ChannelRedirectSettingsDao.getTargetMqName(
//                TEST_URL, TEST_USER, TEST_PASS, TEST_MQ_NAME);
//
//            assertEquals("", result);
//        }
//    }
//
//    @Test
//    void testGetOriginListDatabaseError() throws Exception {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenThrow(new RuntimeException("Database error"));
//
//            String result = ChannelRedirectSettingsDao.getOriginList(
//                TEST_URL, TEST_USER, TEST_PASS, TEST_MQ_NAME);
//
//            assertEquals("", result);
//        }
//    }
//
//    @Test
//    void testGetTargetMqNameSuccessButErrorClose() throws SQLException {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//            PreparedStatement mockPreparedStatement = Mockito.mock(PreparedStatement.class);
//            ResultSet mockResultSet = Mockito.mock(ResultSet.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenReturn(mockPreparedStatement);
//            Mockito.when(mockPreparedStatement.executeQuery())
//                .thenReturn(mockResultSet);
//            Mockito.when(mockResultSet.next()).thenReturn(true, false);
//            Mockito.when(mockResultSet.getString("target_mq_name"))
//                .thenReturn("target.queue");
//            Mockito.doThrow(new SQLException("Database error")).when(mockResultSet).close();
//
//            String result = ChannelRedirectSettingsDao.getTargetMqName(
//                TEST_URL, TEST_USER, TEST_PASS, TEST_MQ_NAME);
//
//            assertEquals("target.queue", result);
//        }
//    }
//
//
//}