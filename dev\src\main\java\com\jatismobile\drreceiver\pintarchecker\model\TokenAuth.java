package com.jatismobile.drreceiver.pintarchecker.model;

import java.time.LocalDateTime;

@lombok.AllArgsConstructor
@lombok.NoArgsConstructor
@lombok.Getter
@lombok.Setter
@lombok.ToString
public class TokenAuth {
    private String token;
    private LocalDateTime expired;

    public boolean isExpired() {
        if (expired == null) {
            return true;
        }
        return LocalDateTime.now().isAfter(expired);
    }
}
