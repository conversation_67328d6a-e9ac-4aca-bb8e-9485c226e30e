package com.jatismobile.drreceiver.pintarchecker;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.boot.SpringApplication;

import com.jatismobile.drreceiver.pintarchecker.configuration.ExceptionConfig.HolidayException;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

class TransmitterFastSignalSpringApplicationTests {

    @Test
    void contextLoadsSuccess() {
        String[] args = new String[]{};
        try (MockedStatic<SpringApplication> mockedStatic = mockStatic(SpringApplication.class)) {
            mockedStatic.when(() -> SpringApplication.run(
                DrReceiverPintarCheckerSpringApplication.class, args))
                .thenReturn(null);
            
            DrReceiverPintarCheckerSpringApplication.main(args);
            
            mockedStatic.verify(() -> SpringApplication.run(
                DrReceiverPintarCheckerSpringApplication.class, args));
        }
    }

    @Test 
    void contextLoadsHolidayException(){
        String[] args = new String[]{};
        try (MockedStatic<SpringApplication> mockedSpring = mockStatic(SpringApplication.class);
             MockedStatic<AppLogUtil> mockedLogger = mockStatic(AppLogUtil.class);
             MockedStatic<Runtime> mockedRuntime = mockStatic(Runtime.class);) {
            
            Runtime mockedRuntimeObj = mock(Runtime.class);

            mockedRuntime.when(Runtime::getRuntime).thenReturn(mockedRuntimeObj);
            doNothing().when(mockedRuntimeObj).exit(1);
            
            mockedSpring.when(() -> SpringApplication.run(
                DrReceiverPintarCheckerSpringApplication.class, args))
                .thenThrow(new HolidayException(""));
            
                DrReceiverPintarCheckerSpringApplication.main(args);
            
            mockedLogger.verify(() -> 
                AppLogUtil.writeInfoLog(null, "Today is holiday. Aborting startup."));
        }
    }

    @Test
    void testContractor() {
        DrReceiverPintarCheckerSpringApplication app = new DrReceiverPintarCheckerSpringApplication();
        assertNotNull(app);
    }
}
