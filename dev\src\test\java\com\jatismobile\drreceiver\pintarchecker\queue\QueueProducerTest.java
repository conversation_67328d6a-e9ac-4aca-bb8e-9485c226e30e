package com.jatismobile.drreceiver.pintarchecker.queue;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

@ExtendWith(MockitoExtension.class)
class QueueProducerTest {
    @Mock
    private JmsTemplate jmsTemplate;

    private QueueProducer queueProducer;

    private String guid = "test-guid";
    private String queueName = "test-queue";
    private String message = "test-message";
    private Integer priority = 4;

    @BeforeEach
    void setUp() {
        queueProducer = new QueueProducer(jmsTemplate);
        ReflectionTestUtils.setField(queueProducer, "appName", "TestApp");
    }

    @Test
    void testSendMessageSuccess() {

        queueProducer.sendMessage(guid, queueName, message, priority);

        verify(jmsTemplate).setPriority(priority);
        verify(jmsTemplate).convertAndSend(queueName, message);
    }

    @Test
    void testSendMessageWithNullPriority() {
        queueProducer.sendMessage(guid, queueName, message, 0);

        verify(jmsTemplate).setPriority(0);
        verify(jmsTemplate).convertAndSend(queueName, message);
    }

    @Test
    void testSendMessageWithEmptyMessage() {
        queueProducer.sendMessage(guid, queueName, "", priority);

        verify(jmsTemplate).setPriority(priority);
        verify(jmsTemplate).convertAndSend(queueName, "");
    }

    @Test
    void testSendMessageThrowsException() {
        RuntimeException expectedException = new RuntimeException("Test exception");
        doThrow(expectedException).when(jmsTemplate).convertAndSend(anyString(), anyString());

        try {
            queueProducer.sendMessage(guid, queueName, message, priority);
        } catch (RuntimeException ex) {
            verify(jmsTemplate).setPriority(priority);
            verify(jmsTemplate).convertAndSend(queueName, message);
        }
    }

    @Test
    void testSendMessageMultipleMessages() {
        String message1 = "test-message-1";
        String message2 = "test-message-2";

        queueProducer.sendMessage(guid, queueName, message1, priority);
        queueProducer.sendMessage(guid, queueName, message2, priority);

        verify(jmsTemplate, times(2)).setPriority(priority);
        verify(jmsTemplate).convertAndSend(queueName, message1);
        verify(jmsTemplate).convertAndSend(queueName, message2);
    }
    
    @Test
    void testStartUp(){
        try (MockedStatic<AppLogUtil> mockAppLogUtil = Mockito.mockStatic(AppLogUtil.class)) {
            
            queueProducer.onStartup(null);
            
            mockAppLogUtil.verify(() -> AppLogUtil.writeInfoLog(null, "[TestApp] | QueueProducer is ready."), times(1));
        }
    }
}
