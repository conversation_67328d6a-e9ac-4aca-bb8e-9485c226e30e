package com.jatismobile.drreceiver.pintarchecker.configuration.db;

import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;
import com.zaxxer.hikari.HikariDataSource;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
		entityManagerFactoryRef = "appsconfigEntityManagerFactory",
		transactionManagerRef = "appsconfigTransactionManager",
		basePackages = {"com.jatismobile.drreceiver.pintarchecker.repository.mysql.appsconfig"}
)
public class AppsConfigConfiguration {
	@Value("${custom.app.name}")
	private String appName;

	@EventListener
	public void onStartup(ApplicationReadyEvent event) {
		AppLogUtil.writeInfoLog(null, "[" + appName + "] | " + getClass().getSimpleName().split("\\$")[0] + " is ready.");
	}
	
	@Bean(name = "appsconfigDataSourceProperties")
	@ConfigurationProperties(prefix = "app.datasource.apps-config")
	public DataSourceProperties customDataSourceProperties() {
		return new DataSourceProperties();
	}

	@Bean(name = "appsconfigDataSource")
	@ConfigurationProperties(prefix = "app.datasource.apps-config.pool")
	public HikariDataSource customDataSource(@Qualifier("appsconfigDataSourceProperties") DataSourceProperties dataSourceProperties) {
		return dataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
	}

	@Bean(name = "appsconfigEntityManagerFactory")
	public LocalContainerEntityManagerFactoryBean customEntityManagerFactory(EntityManagerFactoryBuilder builder, @Qualifier("appsconfigDataSource") DataSource dataSource) {
		return builder
				.dataSource(dataSource)
				.packages("com.jatismobile.drreceiver.pintarchecker.entity.mysql.appsconfig")
				.build();
	}

	@Bean(name = "appsconfigTransactionManager")
	public PlatformTransactionManager customTransactionManager(@Qualifier("appsconfigEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
		return new JpaTransactionManager(entityManagerFactory);
	}
}