//package com.jatismobile.drreceiver.pintarchecker.dao;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.mockStatic;
//import static org.mockito.Mockito.when;
//
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//import java.util.Map;
//
//import org.junit.jupiter.api.Test;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//
//
//class ProviderDaoTest {
//
//    private static final String URL = "**********************************";
//    private static final String USER = "testuser";
//    private static final String PASS = "testpass";
//    private static final String TABLE = "providers";
//
//    @Test
//    void testFindAllSuccess() throws SQLException {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        ResultSet mockResultSet = mock(ResultSet.class);
//
//        try (MockedStatic<DriverManager> driverManagerMock = Mockito.mockStatic(DriverManager.class)) {
//            driverManagerMock.when(() -> DriverManager.getConnection(URL, USER, PASS)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(Mockito.anyString())).thenReturn(mockPreparedStatement);
//            when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
//            when(mockResultSet.next()).thenReturn(true, true, false);
//            when(mockResultSet.getString("id")).thenReturn("1", "2");
//            when(mockResultSet.getString("name")).thenReturn("Provider1", "Provider2");
//
//            Map<String, String> result = ProviderDao.findAll(URL, USER, PASS, TABLE);
//
//            assertNotNull(result);
//            assertEquals(2, result.size());
//            assertEquals("Provider1", result.get("1"));
//            assertEquals("Provider2", result.get("2"));
//        }
//    }
//
//    @Test
//    void testFindAllEmpty() throws SQLException {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        ResultSet mockResultSet = mock(ResultSet.class);
//
//        try (MockedStatic<DriverManager> driverManagerMock = Mockito.mockStatic(DriverManager.class)) {
//            driverManagerMock.when(() -> DriverManager.getConnection(URL, USER, PASS)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(Mockito.anyString())).thenReturn(mockPreparedStatement);
//            when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
//            when(mockResultSet.next()).thenReturn(false);
//
//            Map<String, String> result = ProviderDao.findAll(URL, USER, PASS, TABLE);
//
//            assertNotNull(result);
//            assertTrue(result.isEmpty());
//        }
//    }
//
//    @Test
//    void testFindAllWithSQLException() throws SQLException {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        ResultSet mockResultSet = mock(ResultSet.class);
//        try (MockedStatic<DriverManager> driverManager = mockStatic(DriverManager.class)) {
//            driverManager.when(() -> DriverManager.getConnection(URL, USER, PASS)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
//            when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
//            Mockito.doThrow(new SQLException("error column")).when(mockResultSet).next();
//
//            Map<String, String> result = ProviderDao.findAll(URL, USER, PASS, TABLE);
//
//            assertNotNull(result);
//            assertTrue(result.isEmpty());
//        }
//    }
//
//    @Test
//    void testFindAllWithNullValues() throws SQLException {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        ResultSet mockResultSet = mock(ResultSet.class);
//
//        try (MockedStatic<DriverManager> driverManagerMock = Mockito.mockStatic(DriverManager.class)) {
//            driverManagerMock.when(() -> DriverManager.getConnection(URL, USER, PASS)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(Mockito.anyString())).thenReturn(mockPreparedStatement);
//            when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
//            when(mockResultSet.next()).thenReturn(true, false);
//            when(mockResultSet.getString("id")).thenReturn(null);
//            when(mockResultSet.getString("name")).thenReturn(null);
//
//            Map<String, String> result = ProviderDao.findAll(URL, USER, PASS, TABLE);
//
//            assertNotNull(result);
//            assertEquals(1, result.size());
//            assertTrue(result.containsKey(null));
//            assertEquals(null, result.get(null));
//        }
//    }
//}