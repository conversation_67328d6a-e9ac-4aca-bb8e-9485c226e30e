package com.jatismobile.drreceiver.pintarchecker.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SendSmsResponseTest {

    @Test
    void testSendSmsResponseSettersAndGetters() {
        // Create DataDetails object
        SendSmsResponse.DataDetails dataDetails = new SendSmsResponse.DataDetails();
        dataDetails.setTransactionId(123);
        dataDetails.setReferenceId("REF123");
        dataDetails.setCustomerId("CUST456");
        dataDetails.setOtpCode("OTP789");
        dataDetails.setMessage("Test message");
        dataDetails.setStatus(1);
        dataDetails.setCreatedAt("2025-01-01T10:00:00Z");
        dataDetails.setExpiresAt("2025-01-01T10:30:00Z");

        // Validate DataDetails values
        assertEquals(123, dataDetails.getTransactionId());
        assertEquals("REF123", dataDetails.getReferenceId());
        assertEquals("CUST456", dataDetails.getCustomerId());
        assertEquals("OTP789", dataDetails.getOtpCode());
        assertEquals("Test message", dataDetails.getMessage());
        assertEquals(1, dataDetails.getStatus());
        assertEquals("2025-01-01T10:00:00Z", dataDetails.getCreatedAt());
        assertEquals("2025-01-01T10:30:00Z", dataDetails.getExpiresAt());

        // Create SendSmsResponse object
        SendSmsResponse response = new SendSmsResponse();
        response.setStatus(200);
        response.setCode(0);
        response.setMessage("Success");
        response.setData(dataDetails);

        // Validate SendSmsResponse values
        assertEquals(200, response.getStatus());
        assertEquals(0, response.getCode());
        assertEquals("Success", response.getMessage());
        assertEquals(dataDetails, response.getData());
    }

    @Test
    void testConstructors() {
        // Test DataDetails constructor
        SendSmsResponse.DataDetails dataDetails = new SendSmsResponse.DataDetails(
                123, "REF123", "CUST456", "OTP789", "Test message", 1,
                "2025-01-01T10:00:00Z", "2025-01-01T10:30:00Z"
        );

        assertNotNull(dataDetails);
        assertEquals(123, dataDetails.getTransactionId());
        assertEquals("REF123", dataDetails.getReferenceId());
        assertEquals("CUST456", dataDetails.getCustomerId());

        // Test SendSmsResponse constructor
        SendSmsResponse response = new SendSmsResponse(200, 0, "Success", dataDetails);

        assertNotNull(response);
        assertEquals(200, response.getStatus());
        assertEquals(0, response.getCode());
        assertEquals("Success", response.getMessage());
        assertEquals(dataDetails, response.getData());
    }

    @Test
    void testEqualsAndHashCode() {
        // Create two identical DataDetails objects
        SendSmsResponse.DataDetails data1 = new SendSmsResponse.DataDetails(
                123, "REF123", "CUST456", "OTP789", "Test message", 1,
                "2025-01-01T10:00:00Z", "2025-01-01T10:30:00Z"
        );
        SendSmsResponse.DataDetails data2 = new SendSmsResponse.DataDetails(
                123, "REF123", "CUST456", "OTP789", "Test message", 1,
                "2025-01-01T10:00:00Z", "2025-01-01T10:30:00Z"
        );

        // Validate equality and hash codes
        assertEquals(data1, data2);
        assertEquals(data1.hashCode(), data2.hashCode());

        // Create two identical SendSmsResponse objects
        SendSmsResponse response1 = new SendSmsResponse(200, 0, "Success", data1);
        SendSmsResponse response2 = new SendSmsResponse(200, 0, "Success", data2);

        assertEquals(response1, response2);
        assertEquals(response1.hashCode(), response2.hashCode());
    }

    @Test
    void testToString() {
        // Create DataDetails object
        SendSmsResponse.DataDetails dataDetails = new SendSmsResponse.DataDetails(
                123, "REF123", "CUST456", "OTP789", "Test message", 1,
                "2025-01-01T10:00:00Z", "2025-01-01T10:30:00Z"
        );

        SendSmsResponse response = new SendSmsResponse(200, 0, "Success", dataDetails);

        String expected = "SendSmsResponse(status=200, code=0, message=Success, data=SendSmsResponse.DataDetails(transactionId=123, referenceId=REF123, customerId=CUST456, otpCode=OTP789, message=Test message, status=1, createdAt=2025-01-01T10:00:00Z, expiresAt=2025-01-01T10:30:00Z))";

        // Validate toString format
        assertEquals(expected, response.toString());
    }

    @Test
    void testDataDetailsToString() {
        // Create DataDetails object
        SendSmsResponse.DataDetails dataDetails = new SendSmsResponse.DataDetails(
                123, "REF123", "CUST456", "OTP789", "Test message", 1,
                "2025-01-01T10:00:00Z", "2025-01-01T10:30:00Z"
        );

        // Validate toString format
        String expected = "SendSmsResponse.DataDetails(transactionId=123, referenceId=REF123, customerId=CUST456, otpCode=OTP789, message=Test message, status=1, createdAt=2025-01-01T10:00:00Z, expiresAt=2025-01-01T10:30:00Z)";
        assertEquals(expected, dataDetails.toString());
    }
}
