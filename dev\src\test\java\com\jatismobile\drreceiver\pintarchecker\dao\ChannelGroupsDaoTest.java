//package com.jatismobile.drreceiver.pintarchecker.dao;
//
//import org.junit.jupiter.api.Test;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.mockito.ArgumentMatchers.anyInt;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.mockStatic;
//import static org.mockito.Mockito.when;
//
//import java.sql.Connection;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//import java.sql.DriverManager;
//
//class ChannelGroupsDaoTest {
//
//    private static final String TEST_URL = "**********************************";
//    private static final String TEST_USER = "testuser";
//    private static final String TEST_PASS = "testpass";
//    private static final String TEST_QUEUE = "test.queue";
//
//    @Test
//    void testGetChannelGroupNameSuccess() throws SQLException {
//        String expectedName = "TestGroup";
//
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//            PreparedStatement mockPreparedStatement = Mockito.mock(PreparedStatement.class);
//            ResultSet mockResultSet = Mockito.mock(ResultSet.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenReturn(mockPreparedStatement);
//            Mockito.when(mockPreparedStatement.executeQuery())
//                .thenReturn(mockResultSet);
//            Mockito.when(mockResultSet.next())
//                .thenReturn(true);
//            Mockito.when(mockResultSet.getString("name"))
//                .thenReturn(expectedName);
//
//            String result = ChannelGroupsDao.getChannelGroupName(TEST_URL, TEST_USER, TEST_PASS, TEST_QUEUE);
//            assertEquals(expectedName, result);
//        }
//    }
//
//    @Test
//    void testGetChannelGroupNameNoResults() throws SQLException {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//            PreparedStatement mockPreparedStatement = Mockito.mock(PreparedStatement.class);
//            ResultSet mockResultSet = Mockito.mock(ResultSet.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenReturn(mockPreparedStatement);
//            Mockito.when(mockPreparedStatement.executeQuery())
//                .thenReturn(mockResultSet);
//            Mockito.when(mockResultSet.next())
//                .thenReturn(false);
//
//            String result = ChannelGroupsDao.getChannelGroupName(TEST_URL, TEST_USER, TEST_PASS, TEST_QUEUE);
//            assertEquals("", result);
//        }
//    }
//
//    @Test
//    void testGetChannelGroupNameSQLException() throws SQLException {
//        Connection mockConnection = mock(Connection.class);
//        PreparedStatement mockPreparedStatement = mock(PreparedStatement.class);
//        try (MockedStatic<DriverManager> driverManager = mockStatic(DriverManager.class)) {
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS)).thenReturn(mockConnection);
//            when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
//            Mockito.doThrow(new SQLException("Database connection failed")).when(mockPreparedStatement).setString(anyInt(), anyString());
//
//            String result = ChannelGroupsDao.getChannelGroupName(TEST_URL, TEST_USER, TEST_PASS, TEST_QUEUE);
//            assertEquals("", result);
//        }
//    }
//
//    @Test
//    void testGetChannelGroupNameWithNullQueue() throws SQLException {
//        try (MockedStatic<DriverManager> driverManager = Mockito.mockStatic(DriverManager.class)) {
//            Connection mockConnection = Mockito.mock(Connection.class);
//            PreparedStatement mockPreparedStatement = Mockito.mock(PreparedStatement.class);
//            ResultSet mockResultSet = Mockito.mock(ResultSet.class);
//
//            driverManager.when(() -> DriverManager.getConnection(TEST_URL, TEST_USER, TEST_PASS))
//                .thenReturn(mockConnection);
//
//            Mockito.when(mockConnection.prepareStatement(Mockito.anyString()))
//                .thenReturn(mockPreparedStatement);
//            Mockito.when(mockPreparedStatement.executeQuery())
//                .thenReturn(mockResultSet);
//            Mockito.when(mockResultSet.next())
//                .thenReturn(false);
//
//            String result = ChannelGroupsDao.getChannelGroupName(TEST_URL, TEST_USER, TEST_PASS, null);
//            assertEquals("", result);
//        }
//    }
//}
