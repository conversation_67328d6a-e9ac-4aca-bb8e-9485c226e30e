//package com.jatismobile.drreceiver.pintarchecker.queue;
//
//import static org.mockito.Mockito.*;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.boot.context.event.ApplicationReadyEvent;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
//import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;
//
//@ExtendWith(MockitoExtension.class)
//class TopicListenerTest {
//
//    @Mock
//    private MyGlobalConfig myGlobalConfig;
//
//    @Mock
//    private ApplicationReadyEvent applicationReadyEvent;
//
//    @InjectMocks
//    private TopicListener topicListener;
//
//    @BeforeEach
//    void setUp() {
//        ReflectionTestUtils.setField(topicListener, "appName", "TestApp");
//        ReflectionTestUtils.setField(topicListener, "reloadAll", "RELOAD_ALL");
//        ReflectionTestUtils.setField(topicListener, "reloadChannelsConfig", "RELOAD_CHANNELS");
//        ReflectionTestUtils.setField(topicListener, "reloadProvidersConfig", "RELOAD_PROVIDERS");
//    }
//
//    @Test
//    void testStartUp(){
//        try (MockedStatic<AppLogUtil> mockAppLogUtil = Mockito.mockStatic(AppLogUtil.class)) {
//
//            topicListener.onStartup(null);
//
//            mockAppLogUtil.verify(() -> AppLogUtil.writeInfoLog(null, "[TestApp] | TopicListener is ready."), times(1));
//        }
//    }
//
//    @Test
//    void receiveMessageFromTopicWhenReloadAllShouldInitAllConfig() {
//        topicListener.receiveMessageFromTopic("RELOAD_ALL");
//        verify(myGlobalConfig, times(1)).initConfig();
//        verify(myGlobalConfig, never()).initChannelConfig();
//        verify(myGlobalConfig, never()).initProvidersConfig();
//        verify(myGlobalConfig, never()).initRerouteConfig();
//    }
//
//    @Test
//    void receiveMessageFromTopicWhenReloadChannelsShouldInitChannelConfig() {
//        topicListener.receiveMessageFromTopic("RELOAD_CHANNELS");
//        verify(myGlobalConfig, never()).initConfig();
//        verify(myGlobalConfig, times(1)).initChannelConfig();
//        verify(myGlobalConfig, never()).initProvidersConfig();
//    }
//
//    @Test
//    void receiveMessageFromTopicWhenReloadProvidersShouldInitProvidersConfig() {
//        topicListener.receiveMessageFromTopic("RELOAD_PROVIDERS");
//        verify(myGlobalConfig, never()).initConfig();
//        verify(myGlobalConfig, never()).initChannelConfig();
//        verify(myGlobalConfig, times(1)).initProvidersConfig();
//    }
//
//    @Test
//    void receiveMessageFromTopicWhenUndefinedCommandShouldDoNothing() {
//        topicListener.receiveMessageFromTopic("UNDEFINED_COMMAND");
//        verify(myGlobalConfig, never()).initConfig();
//        verify(myGlobalConfig, never()).initChannelConfig();
//        verify(myGlobalConfig, never()).initProvidersConfig();
//    }
//
//    @Test
//    void receiveMessageFromTopicWhenConfigThrowsExceptionShouldHandleError() {
//        doThrow(new RuntimeException("Config error")).when(myGlobalConfig).initConfig();
//        topicListener.receiveMessageFromTopic("RELOAD_ALL");
//        verify(myGlobalConfig, times(1)).initConfig();
//    }
//}
