package com.jatismobile.drreceiver.pintarchecker.service;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.model.HttpResult;
import com.jatismobile.drreceiver.pintarchecker.model.SendSmsRequest;
import com.jatismobile.drreceiver.pintarchecker.model.TokenAuth;
import com.jatismobile.drreceiver.pintarchecker.model.TokenRequest;
import com.jatismobile.drreceiver.pintarchecker.model.TokenResponse;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import okhttp3.Response;

@Service
public class HttpRequestService {
    private static final String CLASS_NAME = "[HttpRequestService] ";

    private final MyGlobalConfig myGlobalConfig;

    private OkHttpClient okHttpClient;

    private final Gson gson = new Gson();

    private TokenAuth tokenAuth = new TokenAuth();

    @Autowired
    public HttpRequestService(MyGlobalConfig myGlobalConfig, OkHttpClient okHttpClient) {
        this.myGlobalConfig = myGlobalConfig;
        this.okHttpClient = okHttpClient;
    }

    public HttpResult sendSms(String jobId, String email, String password, String msisdn, String message) {
        AppLogUtil.writeInfoLog(jobId, CLASS_NAME + "[sendSms] Started Process");
        Long t1 = System.currentTimeMillis();

        HttpResult httpResult = new HttpResult();

        String providerTrxId = "fastsignal-".concat(String.valueOf(System.currentTimeMillis()));

        SendSmsRequest sendRequest = new SendSmsRequest(providerTrxId, msisdn, "", message);

        String bearerToken = "Bearer ".concat(getToken(jobId, email, password));

        Response response = postRequest(jobId, myGlobalConfig.getLookupConfigMap().get(ConfigKey.PROVIDER_HOST),
                "", bearerToken, MediaType.APPLICATION_JSON_VALUE, gson.toJson(sendRequest));

        AppLogUtil.writeInfoLog(jobId, CLASS_NAME + "[sendSms] Request with bearer token " + bearerToken);
        if (response == null) {
            AppLogUtil.writeInfoLog(jobId, CLASS_NAME + "[sendSms] Response is null or not successfull ");
        } else {
            try {
                String responseBodyString = response.body().string();
                AppLogUtil.writeInfoLog(jobId, CLASS_NAME + "[sendSms] Response body: " + responseBodyString);

                httpResult.setStatusCode(response.code());
                httpResult.setResponse(responseBodyString);

                if (response.code() == HttpStatus.OK.value()) {
                    httpResult.setSuccess(true);
                    httpResult.setCanRetry(false);
                    httpResult.setHasError(false);
                }
            } catch (Exception e) {
                AppLogUtil.writeErrorLog(jobId, CLASS_NAME + "[sendSms] error when read body caused by ", e);
            }
        }
        AppLogUtil.writeInfoLog(jobId, CLASS_NAME
                + "[sendSms] Processing time : ".concat(String.valueOf(System.currentTimeMillis() - t1) + " ms"));
        return httpResult;
    }

    public String getToken(String jobId, String email, String password){
        boolean isGetNewToken = false;
        if (tokenAuth.isExpired()) {
            isGetNewToken = true;
        }

        if (isGetNewToken) {
            tokenAuth = new TokenAuth();
            TokenRequest tokenRequest = new TokenRequest(email, password);
            AppLogUtil.writeInfoLog(jobId, "[getToken] Token is null or Expired, request new token with credentials user: " + email + " and password: " + password);

            Response response = postRequest(jobId, myGlobalConfig.getLookupConfigMap().get(ConfigKey.LOGIN_URL), "", "", MediaType.APPLICATION_JSON_VALUE, gson.toJson(tokenRequest));

            if (response == null) {
                AppLogUtil.writeInfoLog(jobId, CLASS_NAME + "[sendSms] Response is null or not successfull ");
            } else {
                try {
                    String responseBodyString = response.body().string();
                    AppLogUtil.writeInfoLog(jobId, CLASS_NAME + "[sendSms] Response body: " + responseBodyString);

                    AppLogUtil.writeInfoLog(jobId, "[getToken] Body Response: " + responseBodyString);
                    TokenResponse tokenResponse = gson.fromJson(responseBodyString, TokenResponse.class);

                    LocalDateTime tokenExpirationInDate = LocalDateTime.now().plusMinutes(Long.valueOf(myGlobalConfig.getLookupConfigMap().get(ConfigKey.THRESHOLD_TOKEN_EXPIRED_MINUTES)));

                    tokenAuth.setToken(tokenResponse.getData().getToken());
                    tokenAuth.setExpired(tokenExpirationInDate);
                } catch (Exception e) {
                    AppLogUtil.writeErrorLog(jobId, CLASS_NAME + "[sendSms] error when read body caused by ", e);
                }
            }
        }

        return tokenAuth.getToken();
    }

    public Response postRequest(String jobId, String url, String xApiKey, String headerAuthorization, String contentType,
            String bodyString) {
        Long t1 = System.currentTimeMillis();
        Response response = null;
        try {
            RequestBody body = RequestBody.create(bodyString, okhttp3.MediaType.parse(contentType));

            Builder reqBuilder = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("Content-Type", contentType);

            if (!xApiKey.isEmpty()) {
                reqBuilder = reqBuilder.addHeader("X-API-KEY", xApiKey);
            }

            if (!headerAuthorization.isEmpty()) {
                reqBuilder = reqBuilder.addHeader("Authorization", headerAuthorization);
            }

            Request request = reqBuilder.build();
            AppLogUtil.writeInfoLog(jobId, CLASS_NAME
                    + "[postRequest] post request to ".concat(url).concat(" with header ").concat(request.headers().toString()).concat(" with body request ").concat(bodyString));
            response = okHttpClient.newCall(request).execute();
            AppLogUtil.writeInfoLog(jobId,
                    CLASS_NAME + "[postRequest] result with status code = ".concat(String.valueOf(response.code())));
        } catch (Exception e) {
            AppLogUtil.writeErrorLog(jobId,
                    CLASS_NAME + "[postRequest] error when post request to ".concat(url).concat(" caused by "), e);
        }

        AppLogUtil.writeInfoLog(jobId, CLASS_NAME
                + "[postRequest] Processing time : ".concat(String.valueOf(System.currentTimeMillis() - t1) + " ms"));

        return response;
    }
}
