package com.jatismobile.drreceiver.pintarchecker.dao;

import com.mongodb.client.*;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;
import com.mongodb.ConnectionString;

import static com.mongodb.client.model.Filters.eq;

@Component
public class MongoDao {
    private final MongoClient mongoClient;

    @Autowired
    public MongoDao(MyGlobalConfig myGlobalConfig) {
        String connection = String.format("mongodb://%s:%s@%s:%s/?authSource=%s",
        myGlobalConfig.getLookupConfigMap().get(ConfigKey.MONGODB_USERNAME),
        myGlobalConfig.getLookupConfigMap().get(ConfigKey.MONGODB_PASSWORD),
            myGlobalConfig.getLookupConfigMap().get(ConfigKey.MONGODB_HOST),
			myGlobalConfig.getLookupConfigMap().get(ConfigKey.MONGODB_PORT),
			myGlobalConfig.getLookupConfigMap().get(ConfigKey.MONGODB_AUTH_DATABASE)
        );

        AppLogUtil.writeInfoLog(null, "Try connecting to MongoDB: " + connection);

        ConnectionString connectionString = new ConnectionString(connection);
        mongoClient = MongoClients.create(connectionString);
    }

    public String findProviderTrxId(String database, String collection, String providerTrxId) {
        String queueMessage = "";

        // Ambil database dan koleksi
        MongoDatabase mongoDatabase = mongoClient.getDatabase(database);
        MongoCollection<Document> getCollection = mongoDatabase.getCollection(collection);


        // Query pencarian
        FindIterable<Document> results = getCollection.find(eq("provider_trx_id", providerTrxId));

        // Cetak hasil
        for (Document doc : results) {
            queueMessage = doc.getString("queue_message");
        }
        return queueMessage;
    }


}

