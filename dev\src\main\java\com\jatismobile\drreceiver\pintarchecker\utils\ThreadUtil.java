package com.jatismobile.drreceiver.pintarchecker.utils;

import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

public class ThreadUtil {
	private ThreadUtil() {
		// utility class, no instances
	}

	public static void sleepAfterSend(String jobId, Integer sleepTime) {
		AppLogUtil.writeInfoLog(jobId, "[sleep] Sleeping..");
		try {
			sleep(sleepTime);
		} catch (InterruptedException e) {
			AppLogUtil.writeErrorLog(jobId, "[sleep] Failed to sleep after send: ", e);
			Thread.currentThread().interrupt();
		}
	}
	
	public static void sleep(long millis) throws InterruptedException {
		Thread.sleep(millis);
	}
}