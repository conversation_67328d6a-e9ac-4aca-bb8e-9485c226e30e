package com.jatismobile.drreceiver.pintarchecker.utils.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AppErrorLogUtil {
	private static final Logger log = LoggerFactory.getLogger(AppErrorLogUtil.class);

	private AppErrorLogUtil(){}

	public static void writeErrorLog(String uuid, String message, Exception ex) {
		if(null!=uuid){
			log.error("{} | {} caused by: {}", uuid, message, ex);
		}else {
			log.error("{} caused by: {}", message, ex);
		}
	}
}
