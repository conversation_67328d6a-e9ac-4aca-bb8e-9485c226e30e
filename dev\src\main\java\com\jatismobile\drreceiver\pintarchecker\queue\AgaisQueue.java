package com.jatismobile.drreceiver.pintarchecker.queue;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.service.EmailService;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.QueryUtil;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AmqLogUtil;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Component
public class AgaisQueue {
    @Value("${custom.app.name}")
    private String appName;
    
    private final QueueProducer queueProducer;
    private final EmailService emailService;
    private final MyGlobalConfig myGlobalConfig;

    @Autowired
    public AgaisQueue(QueueProducer queueProducer, EmailService emailService, MyGlobalConfig myGlobalConfig){
        this.queueProducer = queueProducer;
        this.emailService = emailService;
        this.myGlobalConfig = myGlobalConfig;
    }

    public void sendError(String guid, Integer errorId, String message){
        try {
            queueProducer.sendMessage(guid, myGlobalConfig.getLookupConfigMap().get(ConfigKey.ACTIVEMQ_NOTIFSENDER), generateMessage(errorId, message, myGlobalConfig.getLookupConfigMap().get(ConfigKey.AGAIS_APPLICATION_ID)), 4);
        }catch (Exception ex){
            String msgError = "[AgaisQueue][sendError] Error sending error to agais queue caused by : ";
            AmqLogUtil.writeErrorLog(guid, msgError, ex);
            emailService.send(msgError, ex);
        }
    }

    private String generateMessage(int errorId, String message, String applicationId) throws UnsupportedEncodingException{
        return "errorid=" + errorId + "&message=" + URLEncoder.encode(message, QueryUtil.CHARSET_UTF_8) + "&applicationid=" + applicationId;
    }

    @EventListener
    public void onStartup(ApplicationReadyEvent event) {
        AppLogUtil.writeInfoLog(null, "[" + appName + "] | " + getClass().getSimpleName() + " is ready.");
    }
}
