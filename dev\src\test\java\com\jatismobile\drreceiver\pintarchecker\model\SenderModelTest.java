package com.jatismobile.drreceiver.pintarchecker.model;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.Test;
import java.util.ArrayList;
import java.util.List;

class SenderModelTest {

    @Test
    void testSenderModelConstructorAndGetters() {
        String sender = "TestSender";
        List<MaskCodeSettings> settings = new ArrayList<>();
        settings.add(new MaskCodeSettings());
        
        SenderModel model = new SenderModel(sender, settings);
        
        assertEquals(sender, model.getSender());
        assertEquals(settings, model.getMaskCodeSettings());
    }

    @Test
    void testSenderModelSetters() {
        SenderModel model = new SenderModel();
        String sender = "NewSender";
        List<MaskCodeSettings> settings = new ArrayList<>();
        
        model.setSender(sender);
        model.setMaskCodeSettings(settings);
        
        assertEquals(sender, model.getSender());
        assertEquals(settings, model.getMaskCodeSettings());
    }

    @Test
    void testSenderModelWithNullValues() {
        SenderModel model = new SenderModel(null, null);
        
        assertNull(model.getSender());
        assertNull(model.getMaskCodeSettings());
    }

    @Test
    void testSenderModelWithEmptySettings() {
        String sender = "TestSender";
        List<MaskCodeSettings> settings = new ArrayList<>();
        
        SenderModel model = new SenderModel(sender, settings);
        
        assertEquals(sender, model.getSender());
        assertTrue(model.getMaskCodeSettings().isEmpty());
    }
}
