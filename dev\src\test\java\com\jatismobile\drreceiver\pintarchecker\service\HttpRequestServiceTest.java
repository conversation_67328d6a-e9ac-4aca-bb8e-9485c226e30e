package com.jatismobile.drreceiver.pintarchecker.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import com.google.gson.Gson;
import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.model.HttpResult;
import com.jatismobile.drreceiver.pintarchecker.model.SendSmsResponse;
import com.jatismobile.drreceiver.pintarchecker.model.SendSmsResponse.DataDetails;
import com.jatismobile.drreceiver.pintarchecker.model.TokenAuth;
import com.jatismobile.drreceiver.pintarchecker.model.TokenResponse;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

@ExtendWith(MockitoExtension.class)
class HttpRequestServiceTest {

    private HttpRequestService httpRequestService;

    @Mock
    private MyGlobalConfig myGlobalConfig;

    @Mock
    private OkHttpClient mockClient;

    @Mock
    private Call mockCall;

    private final Gson gson = new Gson();

    private Map<String, String> configMap;

    // Setup
    private String jobId = "test-job";
    private String email = "<EMAIL>";
    private String password = "password";
    private String msisdn = "1234567890";
    private String message = "Test message";
    private String createdAt = "20250110T10:00:00";
    private String expiredAt = "20250110T10:05:00";
    
    private String url = "https://xxx.com";
    private String xApiKey = "test-api-key";
    private String headerAuthorization = "Bearer test-token";
    private String contentType = "application/json";
    private String bodyString = "{\"key\": \"value\"}";

    @BeforeEach
    void setUp() {
        configMap = new HashMap<String, String>(){{
            put(ConfigKey.PROVIDER_HOST, "http://test.com");
            put(ConfigKey.LOGIN_URL, "http://test.com/login");
            put(ConfigKey.THRESHOLD_TOKEN_EXPIRED_MINUTES, "30");
        }};
        httpRequestService = new HttpRequestService(myGlobalConfig, mockClient);
    }

    @Test
    void testSendSmsSuccess(){
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        SendSmsResponse sendSmsResponse = new SendSmsResponse(200, 0, "Success", 
            new DataDetails(1, "provider_trxid", msisdn, "", message, 2, createdAt, expiredAt));

        Response response = new Response.Builder()
                .request(new okhttp3.Request.Builder()
                        .url(configMap.get(ConfigKey.PROVIDER_HOST))
                        .build())
                .protocol(Protocol.HTTP_1_1)
                .code(200) // HTTP status code
                .message("OK") // HTTP status message
                .body(ResponseBody.create(
                        gson.toJson(sendSmsResponse),
                        MediaType.parse("application/json")
                ))
                .build();

        HttpRequestService spyHttpRequestService = Mockito.spy(httpRequestService);

        Mockito.doReturn("test-token").when(spyHttpRequestService).getToken(anyString(), anyString(), anyString());

        Mockito.doReturn(response).when(spyHttpRequestService).postRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());

        // Execute
        HttpResult result = spyHttpRequestService.sendSms(jobId, email, password, msisdn, message);

        // Verify
        assertTrue(result.isSuccess());
        assertEquals(HttpStatus.OK.value(), result.getStatusCode());
        assertFalse(result.isCanRetry());
        assertFalse(result.isHasError());
    }

    @Test
    void testGetTokenNewToken(){
        TokenResponse tokenResponse = new TokenResponse();
        TokenAuth tokenAuth = new TokenAuth();
        tokenAuth.setToken("new-test-token");
        tokenResponse.setData(tokenAuth);
        
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
        
        Response response = new Response.Builder()
                .request(new okhttp3.Request.Builder()
                        .url(configMap.get(ConfigKey.PROVIDER_HOST))
                        .build())
                .protocol(Protocol.HTTP_1_1)
                .code(200) // HTTP status code
                .message("OK") // HTTP status message
                .body(ResponseBody.create(
                        gson.toJson(tokenResponse),
                        MediaType.parse("application/json")
                ))
                .build();

        HttpRequestService spyHttpRequestService = Mockito.spy(httpRequestService);
        
        Mockito.doReturn(response).when(spyHttpRequestService).postRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());

        // Execute
        String token = spyHttpRequestService.getToken(jobId, email, password);

        // Verify
        assertEquals("new-test-token", token);
    }

    @Test
    void testGetTokenExistingValidToken() {
        // Set existing valid token
        TokenResponse tokenResponse = new TokenResponse();
        TokenAuth tokenAuth = new TokenAuth();
        tokenAuth.setToken("existing-token");
        tokenResponse.setData(tokenAuth);
        
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        Response response = new Response.Builder()
                .request(new okhttp3.Request.Builder()
                        .url(configMap.get(ConfigKey.PROVIDER_HOST))
                        .build())
                .protocol(Protocol.HTTP_1_1)
                .code(200) // HTTP status code
                .message("OK") // HTTP status message
                .body(ResponseBody.create(
                        gson.toJson(tokenResponse),
                        MediaType.parse("application/json")
                ))
                .build();

        tokenAuth.setToken("new-get-token");
        tokenResponse.setData(tokenAuth);

        Response response2 = new Response.Builder()
                .request(new okhttp3.Request.Builder()
                        .url(configMap.get(ConfigKey.PROVIDER_HOST))
                        .build())
                .protocol(Protocol.HTTP_1_1)
                .code(200) // HTTP status code
                .message("OK") // HTTP status message
                .body(ResponseBody.create(
                        gson.toJson(tokenResponse),
                        MediaType.parse("application/json")
                ))
                .build();

        HttpRequestService spyHttpRequestService = Mockito.spy(httpRequestService);
        
        Mockito.doReturn(response).doReturn(response2).when(spyHttpRequestService).postRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());

        // First call to set token
        String token = spyHttpRequestService.getToken(jobId, email, password);

        // Verify token is get new
        assertEquals("existing-token", token);
        
        // Execute second call
        String token2 = spyHttpRequestService.getToken(jobId, email, password);

        // Verify token is reused
        assertEquals("existing-token", token2);
    }

    @Test
    void testSendSmsFailure(){
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        Response response = new Response.Builder()
                .request(new okhttp3.Request.Builder()
                        .url(configMap.get(ConfigKey.PROVIDER_HOST))
                        .build())
                .protocol(Protocol.HTTP_1_1)
                .code(500) // HTTP status code
                .message("Error") // HTTP status message
                .body(ResponseBody.create(
                        "Internal Server Error",
                        MediaType.parse("text/plain")
                ))
                .build();

        HttpRequestService spyHttpRequestService = Mockito.spy(httpRequestService);

        Mockito.doReturn("test-token").when(spyHttpRequestService).getToken(anyString(), anyString(), anyString());

        Mockito.doReturn(response).when(spyHttpRequestService).postRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());

        // Execute
        HttpResult result = spyHttpRequestService.sendSms(jobId, email, password, msisdn, message);

        // Verify
        assertFalse(result.isSuccess());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), result.getStatusCode());
        assertTrue(result.isCanRetry());
        assertTrue(result.isHasError());
        assertEquals("Internal Server Error", result.getResponse());
    }

    @Test
    void testSendSmsResponseNull(){
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        HttpRequestService spyHttpRequestService = Mockito.spy(httpRequestService);

        Mockito.doReturn("test-token").when(spyHttpRequestService).getToken(anyString(), anyString(), anyString());

        Mockito.doReturn(null).when(spyHttpRequestService).postRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());

        // Execute
        HttpResult result = spyHttpRequestService.sendSms(jobId, email, password, msisdn, message);

        // Verify
        assertEquals(new HttpResult(), result);
    }

    @Test
    void testSendSmsErrorBodyString(){
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        Response response = new Response.Builder()
                .request(new okhttp3.Request.Builder()
                        .url(configMap.get(ConfigKey.PROVIDER_HOST))
                        .build())
                .protocol(Protocol.HTTP_1_1)
                .code(500) // HTTP status code
                .message("Error") // HTTP status message
                .body(null)
                .build();

        HttpRequestService spyHttpRequestService = Mockito.spy(httpRequestService);

        Mockito.doReturn("test-token").when(spyHttpRequestService).getToken(anyString(), anyString(), anyString());

        Mockito.doReturn(response).when(spyHttpRequestService).postRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());

        // Execute
        HttpResult result = spyHttpRequestService.sendSms(jobId, email, password, msisdn, message);

        // Verify
        assertEquals(new HttpResult(), result);
    }

    @Test
    void testGetTokenResponseNull(){
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        HttpRequestService spyHttpRequestService = Mockito.spy(httpRequestService);

        Mockito.doReturn(null).when(spyHttpRequestService).postRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());

        // Execute
        String token = spyHttpRequestService.getToken(jobId, email, password);

        // Verify
        assertNull(token);
    }

    @Test
    void testGetTokenErrorBodyString(){
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        Response response = new Response.Builder()
                .request(new okhttp3.Request.Builder()
                        .url(configMap.get(ConfigKey.PROVIDER_HOST))
                        .build())
                .protocol(Protocol.HTTP_1_1)
                .code(500) // HTTP status code
                .message("Error") // HTTP status message
                .body(null)
                .build();

        HttpRequestService spyHttpRequestService = Mockito.spy(httpRequestService);

        Mockito.doReturn(response).when(spyHttpRequestService).postRequest(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());

        // Execute
        String token = spyHttpRequestService.getToken(jobId, email, password);

        // Verify
        assertNull(token);
    }

    @Test
    void testPostRequestSuccess() throws Exception {
        // Mock the response
        Response response = new Response.Builder()
                .request(new Request.Builder().url(url).build())
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .build();

        when(mockClient.newCall(any(Request.class))).thenReturn(mockCall);
        when(mockCall.execute()).thenReturn(response);

        // Call the method under test
        Response responseRes = httpRequestService.postRequest(jobId, url, xApiKey, headerAuthorization, contentType, bodyString);

        // Assertions
        assertNotNull(responseRes);
        assertEquals(200, responseRes.code());
    }

    @Test
    void testPostRequestSuccessEmptyApiKeyAndHeader() throws Exception {
        // Mock the response
        Response response = new Response.Builder()
                .request(new Request.Builder().url(url).build())
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .build();

        when(mockClient.newCall(any(Request.class))).thenReturn(mockCall);
        when(mockCall.execute()).thenReturn(response);

        // Call the method under test
        Response responseRes = httpRequestService.postRequest(jobId, url, "", "", contentType, bodyString);

        // Assertions
        assertNotNull(responseRes);
        assertEquals(200, responseRes.code());
    }

    @Test
    void testPostRequestErrorThrowIOException() throws Exception {
        when(mockClient.newCall(any(Request.class))).thenReturn(mockCall);
        when(mockCall.execute()).thenThrow(IOException.class);

        // Call the method under test
        Response responseRes = httpRequestService.postRequest(jobId, url, "", "", contentType, bodyString);

        // Assertions
        assertNull(responseRes);
    }
}