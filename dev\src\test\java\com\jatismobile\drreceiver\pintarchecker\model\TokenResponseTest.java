package com.jatismobile.drreceiver.pintarchecker.model;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class TokenResponseTest {

    @Test
    void testTokenResponseConstructorAndGetters() {
        TokenAuth tokenAuth = new TokenAuth();
        TokenResponse tokenResponse = new TokenResponse("success", "200", "OK", tokenAuth);
        
        assertEquals("success", tokenResponse.getStatus());
        assertEquals("200", tokenResponse.getCode());
        assertEquals("OK", tokenResponse.getMessage());
        assertEquals(tokenAuth, tokenResponse.getData());
    }

    @Test
    void testTokenResponseSetters() {
        TokenResponse tokenResponse = new TokenResponse();
        TokenAuth tokenAuth = new TokenAuth();
        
        tokenResponse.setStatus("error");
        tokenResponse.setCode("400");
        tokenResponse.setMessage("Bad Request");
        tokenResponse.setData(tokenAuth);
        
        assertEquals("error", tokenResponse.getStatus());
        assertEquals("400", tokenResponse.getCode());
        assertEquals("Bad Request", tokenResponse.getMessage());
        assertEquals(tokenAuth, tokenResponse.getData());
    }

    @Test
    void testTokenResponseToString() {
        TokenAuth tokenAuth = new TokenAuth();
        TokenResponse tokenResponse = new TokenResponse("success", "200", "OK", tokenAuth);
        
        String toString = tokenResponse.toString();
        
        assertTrue(toString.contains("status=success"));
        assertTrue(toString.contains("code=200"));
        assertTrue(toString.contains("message=OK"));
        assertTrue(toString.contains("data=" + tokenAuth.toString()));
    }

    @Test
    void testTokenResponseWithNullValues() {
        TokenResponse tokenResponse = new TokenResponse(null, null, null, null);
        
        assertNull(tokenResponse.getStatus());
        assertNull(tokenResponse.getCode());
        assertNull(tokenResponse.getMessage());
        assertNull(tokenResponse.getData());
    }
}
