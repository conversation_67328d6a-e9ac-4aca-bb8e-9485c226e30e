package com.jatismobile.drreceiver.pintarchecker.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;

import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;

@ExtendWith(MockitoExtension.class)
class EmailServiceTest {

    @Mock
    private JavaMailSender javaMailSender;

    @Mock
    private MyGlobalConfig myGlobalConfig;

    private EmailService emailService;

    private Map<String, String> configMap;

    @BeforeEach
    void setUp() {
        configMap = new HashMap<>();
        configMap.put(ConfigKey.MAIL_TO, "<EMAIL>,<EMAIL>");
        configMap.put(ConfigKey.MAIL_SUBJECT, "Test Subject");
        configMap.put(ConfigKey.MAIL_MESSAGE, "Test Message");
        
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
        emailService = new EmailService(javaMailSender, myGlobalConfig);
    }

    @Test
    void testSendEmailWithValidInput() {
        String message = "Test error message";
        Exception exception = new RuntimeException("Test exception");

        emailService.send(message, exception);

        verify(javaMailSender, times(1)).send(any(SimpleMailMessage.class));
    }

    @Test
    void testSendEmailWithEmptyMessage() {
        String message = "";
        Exception exception = new RuntimeException("Test exception");

        emailService.send(message, exception);

        verify(javaMailSender, times(1)).send(any(SimpleMailMessage.class));
    }

    @Test
    void testSendEmailWithNullException() {
        String message = "Test message";
        Exception exception = new Exception("xxx");

        emailService.send(message, exception);

        verify(javaMailSender, times(1)).send(any(SimpleMailMessage.class));
    }

    @Test
    void testSendEmailWithMultipleRecipients() {
        configMap.put(ConfigKey.MAIL_TO, "<EMAIL>,<EMAIL>,<EMAIL>");
        String message = "Test message";
        Exception exception = new RuntimeException("Test exception");

        emailService.send(message, exception);

        verify(javaMailSender, times(1)).send(any(SimpleMailMessage.class));
    }

    @Test
    void testSendEmailWithLongMessage() {
        String message = new String(new char[1000]).replace("\0", "a");
        Exception exception = new RuntimeException("Test exception");

        emailService.send(message, exception);

        verify(javaMailSender, times(1)).send(any(SimpleMailMessage.class));
    }
}
