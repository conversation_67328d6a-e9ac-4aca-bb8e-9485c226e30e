package com.jatismobile.drreceiver.pintarchecker.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class HashUtil {

    private HashUtil() {
        // Utility class, no instantiation allowed
    }

    /**
     * Generates a SHA-256 hash for the given input string.
     *
     * @param input the input string to hash
     * @return the SHA-256 hash as a hexadecimal string
     */
    public static String hashSHA256(String input) throws HashGenerationException {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new HashGenerationException("Error generating SHA-256 hash", e);
        }
    }

    public static String base64EncodeString(String input) {
        byte[] encodedBytes = Base64.getEncoder().encode(input.getBytes(StandardCharsets.UTF_8));
        return new String(encodedBytes, StandardCharsets.UTF_8);
    }

    public static class HashGenerationException extends Exception {
        public HashGenerationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * Converts an array of bytes into a hexadecimal string.
     *
     * @param bytes the byte array to convert
     * @return the hexadecimal string representation of the byte array
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
