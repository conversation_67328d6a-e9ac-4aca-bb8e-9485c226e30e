package com.jatismobile.drreceiver.pintarchecker.utils.log;

import static org.mockito.Mockito.times;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.slf4j.Logger;

class AppLogUtilTest extends TestLogUtil {

    @Test
    void writeInfoLogWithUUID() throws Exception {
        String uuid = "test-uuid";
        String message = "Test error message";
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AppLogUtil.class.getDeclaredField("log"), mockLogger);
        
        AppLogUtil.writeInfoLog(uuid, message);
        
        Mockito.verify(mockLogger).info("{} | {}", uuid, message);
    }

    @Test
    void writeInfoLogWithoutUUID() throws Exception {
        String message = "Test error message";
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AppLogUtil.class.getDeclaredField("log"), mockLogger);
        
        AppLogUtil.writeInfoLog(null, message);
            
        Mockito.verify(mockLogger).info(message);
    }

    @Test
    void writeInfoLogWithEmptyMessage() throws Exception {
        String uuid = "test-uuid";
        String message = "";
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AppLogUtil.class.getDeclaredField("log"), mockLogger);
            
        AppLogUtil.writeInfoLog(uuid, message);
        
        Mockito.verify(mockLogger).info("{} | {}", uuid, message);
    }
    
    @Test
    void writeErrorLogWithUUID(){
        String uuid = "test-uuid";
        String message = "Test error message";
        Exception exception = new RuntimeException("Test exception");
        
        try (MockedStatic<AppErrorLogUtil> mockAppErrorLog = Mockito.mockStatic(AppErrorLogUtil.class)) {
            
            AppLogUtil.writeErrorLog(uuid, message, exception);
            
            mockAppErrorLog.verify(() -> AppErrorLogUtil.writeErrorLog(uuid, message, exception), times(1));
        }
    }
}
