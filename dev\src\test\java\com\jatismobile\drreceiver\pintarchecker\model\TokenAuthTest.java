package com.jatismobile.drreceiver.pintarchecker.model;

import org.junit.jupiter.api.Test;
import java.time.LocalDateTime;
import static org.junit.jupiter.api.Assertions.*;

class TokenAuthTest {

    @Test
    void isExpiredShouldReturnTrueWhenTokenIsExpired() {
        LocalDateTime expiredTime = LocalDateTime.now().minusMinutes(1);
        TokenAuth tokenAuth = new TokenAuth("test-token", expiredTime);
        
        assertTrue(tokenAuth.isExpired());
    }

    @Test
    void isExpiredShouldReturnFalseWhenTokenIsNotExpired() {
        LocalDateTime futureTime = LocalDateTime.now().plusMinutes(30);
        TokenAuth tokenAuth = new TokenAuth("test-token", futureTime);
        
        assertFalse(tokenAuth.isExpired());
    }

    @Test
    void constructor_ShouldSetAllFields() {
        String expectedToken = "test-token";
        LocalDateTime expectedExpiry = LocalDateTime.now().plusHours(1);
        
        TokenAuth tokenAuth = new TokenAuth(expectedToken, expectedExpiry);
        
        assertEquals(expectedToken, tokenAuth.getToken());
        assertEquals(expectedExpiry, tokenAuth.getExpired());
    }

    @Test
    void settersAndGetters_ShouldWorkCorrectly() {
        TokenAuth tokenAuth = new TokenAuth();
        String expectedToken = "new-token";
        LocalDateTime expectedExpiry = LocalDateTime.now().plusHours(2);
        
        tokenAuth.setToken(expectedToken);
        tokenAuth.setExpired(expectedExpiry);
        
        assertEquals(expectedToken, tokenAuth.getToken());
        assertEquals(expectedExpiry, tokenAuth.getExpired());
    }
    @Test
    void isExpiredShouldReturnTrueWhenExpiryIsNull() {
        TokenAuth tokenAuth = new TokenAuth();
        tokenAuth.setToken("test-token");
        tokenAuth.setExpired(null);
        
        assertTrue(tokenAuth.isExpired());
    }

    @Test
    void isExpiredShouldReturnTrueWhenExpiryIsOneSecondAgo() {
        LocalDateTime oneSecondAgo = LocalDateTime.now().minusSeconds(1);
        TokenAuth tokenAuth = new TokenAuth("test-token", oneSecondAgo);
        
        assertTrue(tokenAuth.isExpired());
    }

    @Test
    void isExpiredShouldReturnFalseWhenExpiryIsOneSecondFromNow() {
        LocalDateTime oneSecondFromNow = LocalDateTime.now().plusSeconds(1);
        TokenAuth tokenAuth = new TokenAuth("test-token", oneSecondFromNow);
        
        assertFalse(tokenAuth.isExpired());
    }
}
