package com.jatismobile.drreceiver.pintarchecker.utils.log;

import static org.mockito.Mockito.times;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.slf4j.Logger;

class AmqLogUtilTest extends TestLogUtil {

    @Test
    void writeInfoLogWithUUID() throws Exception {
        String uuid = "test-uuid";
        String message = "Test error message";
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AmqLogUtil.class.getDeclaredField("log"), mockLogger);
        
        AmqLogUtil.writeInfoLog(uuid, message);
        
        Mockito.verify(mockLogger).info("{} | {}", uuid, message);
    }

    @Test
    void writeInfoLogWithoutUUID() throws Exception {
        String message = "Test error message";
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AmqLogUtil.class.getDeclaredField("log"), mockLogger);
        
        AmqLogUtil.writeInfoLog(null, message);
            
        Mockito.verify(mockLogger).info(message);
    }

    @Test
    void writeInfoLogWithEmptyMessage() throws Exception {
        String uuid = "test-uuid";
        String message = "";
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AmqLogUtil.class.getDeclaredField("log"), mockLogger);
            
        AmqLogUtil.writeInfoLog(uuid, message);
        
        Mockito.verify(mockLogger).info("{} | {}", uuid, message);
    }
    
    @Test
    void writeErrorLogWithUUID(){
        String uuid = "test-uuid";
        String message = "Test error message";
        Exception exception = new RuntimeException("Test exception");
        
        try (MockedStatic<AmqErrorLogUtil> mockAmqErrorLog = Mockito.mockStatic(AmqErrorLogUtil.class)) {
            
            AmqLogUtil.writeErrorLog(uuid, message, exception);
            
            mockAmqErrorLog.verify(() -> AmqErrorLogUtil.writeErrorLog(uuid, message, exception), times(1));
        }
    }
}
