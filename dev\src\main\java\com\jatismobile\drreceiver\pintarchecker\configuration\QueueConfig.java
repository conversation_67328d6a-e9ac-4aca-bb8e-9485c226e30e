package com.jatismobile.drreceiver.pintarchecker.configuration;

import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.activemq.RedeliveryPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jms.DefaultJmsListenerContainerFactoryConfigurer;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.connection.CachingConnectionFactory;
import org.springframework.jms.core.JmsTemplate;

import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

import javax.jms.ConnectionFactory;

@EnableJms
@Configuration
public class QueueConfig {
    @Value("${custom.app.name}")
    private String appName;    
    
    @Value("${app.config.activemq.queue.connection-cache}")
    private Integer connectionCache;
    @Value("${app.config.activemq.queue.concurrency}")
    private String concurrency;
    @Value("${app.config.activemq.queue.max-message-per-task}")
    private Integer maxMessagePerTask;
    @Value("${app.config.activemq.queue.receive-timeout}")
    private Long receiveTimeout;


    private final MyGlobalConfig myGlobalConfig;

    @Autowired
    public QueueConfig(MyGlobalConfig myGlobalConfig) {
        this.myGlobalConfig = myGlobalConfig;
    }

    @Bean
    public ConnectionFactory connectionFactory() {
        @SuppressWarnings("all")
        ActiveMQConnectionFactory connectionFactory =  new ActiveMQConnectionFactory();
        connectionFactory.setBrokerURL(myGlobalConfig.getLookupConfigMap().get(ConfigKey.ACTIVEMQ_HOST));
        connectionFactory.setUserName(myGlobalConfig.getLookupConfigMap().get(ConfigKey.ACTIVEMQ_USERNAME));
        connectionFactory.setPassword(myGlobalConfig.getLookupConfigMap().get(ConfigKey.ACTIVEMQ_PASSWORD));
        connectionFactory.setUseAsyncSend(true);
        connectionFactory.setRedeliveryPolicy(redeliveryPolicy());
        return connectionFactory;
    }

    @Bean
    public RedeliveryPolicy redeliveryPolicy() {
        RedeliveryPolicy redeliveryPolicy = new RedeliveryPolicy();
        redeliveryPolicy.setInitialRedeliveryDelay(10000);
        redeliveryPolicy.setMaximumRedeliveries(3);
        redeliveryPolicy.setUseExponentialBackOff(true);
        redeliveryPolicy.setBackOffMultiplier(2);
        return redeliveryPolicy;
    }

    @Bean
    public ConnectionFactory cachingConnectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setTargetConnectionFactory(connectionFactory());
        connectionFactory.setSessionCacheSize(connectionCache);
        connectionFactory.setCacheProducers(true);
        connectionFactory.setReconnectOnException(true);
        return connectionFactory;
    }

    @Bean
    public DefaultJmsListenerContainerFactory jmsListenerContainerFactory(
            DefaultJmsListenerContainerFactoryConfigurer configurer,
            ConnectionFactory connectionFactory) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        factory.setConcurrency(concurrency);
        factory.setMaxMessagesPerTask(maxMessagePerTask);
        factory.setReceiveTimeout(receiveTimeout);
        return factory;
    }

    @Bean
    public JmsTemplate jmsTemplate(){
        JmsTemplate template = new JmsTemplate();
        template.setExplicitQosEnabled(true);
        template.setConnectionFactory(cachingConnectionFactory());
        template.setDeliveryPersistent(Boolean.valueOf(myGlobalConfig.getLookupConfigMap().get(ConfigKey.QUEUE_PERSISTENCY)));
        return template;
    }

    @EventListener
    public void onStartup(ApplicationReadyEvent event) {
        AppLogUtil.writeInfoLog(null,
                "[" + appName + "] | " + getClass().getSimpleName().split("\\$")[0] + " is ready.");
    }
}
