<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property file="src/main/resources/logback.properties" />

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</Pattern>
        </layout>
    </appender>

    <timestamp key="byDate" datePattern="yyyy-MM-dd"/>

    <appender name="DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${debugPath}${applicationName}.debug.log</file>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${debugPath}${applicationName}.debug.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %logger{35} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${errorPath}${applicationName}.error.log</file>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${errorPath}${applicationName}.error.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %logger{35} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="AMQ-DEBUG"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${amqPath}${applicationName}.amq.log</file>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${amqPath}${applicationName}.amq.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %logger{35} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="AMQ-ERROR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${amqErrorPath}${applicationName}.amqerror.log</file>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${amqErrorPath}${applicationName}.amqerror.%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %logger{35} %msg%n</pattern>
        </encoder>
    </appender>


    <logger name="com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil" level="INFO" additivity="false">
        <appender-ref ref="DEBUG"/>
    </logger>

    <logger name="com.jatismobile.drreceiver.pintarchecker.utils.log.AppErrorLogUtil" level="ERROR" additivity="false">
        <appender-ref ref="ERROR"/>
    </logger>

    <logger name="com.jatismobile.drreceiver.pintarchecker.utils.log.AmqLogUtil" level="INFO" additivity="false">
        <appender-ref ref="AMQ-DEBUG"/>
    </logger>

    <logger name="com.jatismobile.drreceiver.pintarchecker.utils.log.AmqErrorLogUtil" level="ERROR" additivity="false">
        <appender-ref ref="AMQ-ERROR"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>