package com.jatismobile.drreceiver.pintarchecker.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public class QueryUtil {

    public static final String CHARSET_UTF_8 = "UTF-8";

    private QueryUtil(){}

    public static Map<String, String> parseQuery(String query) throws UnsupportedEncodingException{
        Map<String, String> result = new HashMap<>();

        String[] pairs = query.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            result.put(URLDecoder.decode(pair.substring(0, idx), CHARSET_UTF_8),
                    URLDecoder.decode(pair.substring(idx + 1), CHARSET_UTF_8));
        }
        return result;
    }

    public static String queryBuilder(Map<String, String> params) throws UnsupportedEncodingException{
        StringBuilder result = new StringBuilder();
        int size = params.size();
        int counter = 0;
        String value;

        for (Map.Entry<String, String> param : params.entrySet()) {

            result.append(URLEncoder.encode(param.getKey(), CHARSET_UTF_8));
            result.append("=");
            value = param.getValue();
            if (null != value && !value.isEmpty()) {
                result.append(URLEncoder.encode(param.getValue(), CHARSET_UTF_8));
            }
            
            if (size > (++counter)) {
                result.append("&");
            }
        }

        return result.toString();
    }
}
