package com.jatismobile.drreceiver.pintarchecker.model;

import com.google.gson.annotations.SerializedName;

@lombok.Setter
@lombok.Getter
@lombok.ToString
@lombok.EqualsAndHashCode
@lombok.AllArgsConstructor
@lombok.NoArgsConstructor
public class SendSmsRequest {
    @SerializedName("reference_id")
    private String referenceId;
    @SerializedName("customer_id")
    private String customerId;
    @SerializedName("otp_code")
    private String otpCode;
    @SerializedName("message")
    private String message;
}
