package com.jatismobile.drreceiver.pintarchecker.model;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

class TransmitterSettingTest {

    private TransmitterSetting transmitterSetting;

    @BeforeEach
    void setUp() {
        transmitterSetting = new TransmitterSetting();
    }

    @Test
    void testNoArgsConstructor() {
        assertNotNull(transmitterSetting);
        assertNull(transmitterSetting.getSenderID());
        assertNull(transmitterSetting.getUsername());
        assertNull(transmitterSetting.getPassword());
        assertNull(transmitterSetting.getCpName());
        assertEquals(0, transmitterSetting.getChannelIsDirect());
    }

    @Test
    void testAllArgsConstructor() {
        TransmitterSetting setting = new TransmitterSetting("sender123", "testUser", "pass123", "cpTest", 1);
        
        assertEquals("sender123", setting.getSenderID());
        assertEquals("testUser", setting.getUsername());
        assertEquals("pass123", setting.getPassword());
        assertEquals("cpTest", setting.getCpName());
        assertEquals(1, setting.getChannelIsDirect());
    }

    @Test
    void testSettersAndGetters() {
        transmitterSetting.setSenderID("newSender");
        transmitterSetting.setUsername("newUser");
        transmitterSetting.setPassword("newPass");
        transmitterSetting.setCpName("newCP");
        transmitterSetting.setChannelIsDirect(2);

        assertEquals("newSender", transmitterSetting.getSenderID());
        assertEquals("newUser", transmitterSetting.getUsername());
        assertEquals("newPass", transmitterSetting.getPassword());
        assertEquals("newCP", transmitterSetting.getCpName());
        assertEquals(2, transmitterSetting.getChannelIsDirect());
    }

    @Test
    void testEmptyStrings() {
        transmitterSetting.setSenderID("");
        transmitterSetting.setUsername("");
        transmitterSetting.setPassword("");
        transmitterSetting.setCpName("");
        transmitterSetting.setChannelIsDirect(0);

        assertEquals("", transmitterSetting.getSenderID());
        assertEquals("", transmitterSetting.getUsername());
        assertEquals("", transmitterSetting.getPassword());
        assertEquals("", transmitterSetting.getCpName());
        assertEquals(0, transmitterSetting.getChannelIsDirect());
    }

    @Test
    void testNullValues() {
        transmitterSetting.setSenderID(null);
        transmitterSetting.setUsername(null);
        transmitterSetting.setPassword(null);
        transmitterSetting.setCpName(null);

        assertNull(transmitterSetting.getSenderID());
        assertNull(transmitterSetting.getUsername());
        assertNull(transmitterSetting.getPassword());
        assertNull(transmitterSetting.getCpName());
    }
}
