custom.app.name=DrReceiver Pintar Checker

app.config.server-timezone=Asia/Jakarta

# config logback.xml location
logging.config=src/main/resources/logback.xml

# config mysql apps_config (dont use quote)
app.datasource.apps-config.url=******************************************************************************************************************************************=${app.config.server-timezone}&useSSL=false
app.datasource.apps-config.username=root
app.datasource.apps-config.password=Jatis123$$$
app.datasource.apps-config.pool.maximumPoolSize=10

# config app properties (dont use quote)
app.config.lookup-type=drreceiver-pintar-checker

app.config.activemq.queue.connection-cache=50
app.config.activemq.queue.concurrency=1-50
app.config.activemq.queue.max-message-per-task=1
app.config.activemq.queue.receive-timeout=900

app.config.activemq.topic.reload.all=reload
app.config.activemq.topic.reload.channels=reload channel
app.config.activemq.topic.reload.providers=reload provider

#email alert
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=testing-alert
spring.mail.password=mobile14
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

======================================================================================

# Start Don't change =================================================================
spring.main.web-application-type=none

# ## config jpa (dont use quote)
spring.jpa.database=default
spring.jpa.show-sql=false

# END Don't Change =================================================================