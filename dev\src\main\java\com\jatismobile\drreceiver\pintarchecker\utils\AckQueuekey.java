package com.jatismobile.drreceiver.pintarchecker.utils;

public class AckQueuekey {
    public static final String TRXID = "trxid";
    public static final String JOB_ID = "job_id";
    public static final String CLIENT_ID = "client_id";
    public static final String DIVISION_ID = "division_id";
    public static final String MASK_ID = "mask_id";
    public static final String MASK_NAME = "mask_name";
    public static final String PROVIDER_ID = "provider_id";
    public static final String MSISDN = "msisdn";
    public static final String CHANNEL_NAME = "channel_name";
    public static final String PROVIDER_NAME = "provider_name";
    public static final String CHARGECODE = "chargecode";
    public static final String SERVICE_TYPE = "service_type";
    public static final String WITHOUT_DR = "without_dr";
    public static final String MESSAGE_SEQ = "message_seq";
    public static final String CHANNEL_IS_DIRECT = "channel_is_direct";
    public static final String CHANNEL_MQ_NAME = "channel_mq_name";
    public static final String TRANSMITTER_SUBMIT_STATUS = "transmitter_submit_status";
    public static final String PROVIDER_SUBMIT_STATUS = "provider_submit_status";
    public static final String PROVIDER_TRX_ID = "provider_trxid";
    public static final String DATE_HIT = "date_hit";
    public static final String DATE_RESPONSE = "date_response";
    public static final String DURATION = "duration";
    public static final String RETRY = "retry";

    private AckQueuekey(){}
}
