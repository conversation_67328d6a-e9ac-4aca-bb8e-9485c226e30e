package com.jatismobile.drreceiver.pintarchecker.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.jatismobile.drreceiver.pintarchecker.model.MaskCodeSettings;

class MaskCodeSettingsUtilTest {

    @Test
    void testFindByKeyWithValidKeyAndListShouldReturnMatchingSettings() {
        MaskCodeSettings settings1 = new MaskCodeSettings();
        settings1.setKey("key1");
        MaskCodeSettings settings2 = new MaskCodeSettings();
        settings2.setKey("key2");
        
        List<MaskCodeSettings> settingsList = Arrays.asList(settings1, settings2);
        
        MaskCodeSettings result = MaskCodeSettingsUtil.findByKey(settingsList, "key1");
        
        assertEquals(settings1, result);
    }

    @Test
    void testFindByKeyWithNonExistentKeyShouldReturnNull() {
        MaskCodeSettings settings = new MaskCodeSettings();
        settings.setKey("existingKey");
        
        List<MaskCodeSettings> settingsList = Arrays.asList(settings);
        
        MaskCodeSettings result = MaskCodeSettingsUtil.findByKey(settingsList, "nonExistentKey");
        
        assertNull(result);
    }

    @Test
    void testFindByKeyWithNullListShouldReturnNull() {
        MaskCodeSettings result = MaskCodeSettingsUtil.findByKey(null, "anyKey");
        
        assertNull(result);
    }

    @Test
    void testFindByKeyWithNullKeyShouldReturnNull() {
        List<MaskCodeSettings> settingsList = new ArrayList<>();
        
        MaskCodeSettings result = MaskCodeSettingsUtil.findByKey(settingsList, null);
        
        assertNull(result);
    }

    @Test
    void testFindByKeyWithEmptyListShouldReturnNull() {
        List<MaskCodeSettings> settingsList = new ArrayList<>();
        
        MaskCodeSettings result = MaskCodeSettingsUtil.findByKey(settingsList, "anyKey");
        
        assertNull(result);
    }
}
