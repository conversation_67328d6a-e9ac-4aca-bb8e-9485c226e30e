package com.jatismobile.drreceiver.pintarchecker.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.MockedStatic;
import com.jatismobile.drreceiver.pintarchecker.utils.HashUtil.HashGenerationException;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.stream.Stream;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;

class HashUtilTest {

    private static Stream<Arguments> sha256TestData() {
        return Stream.of(
            Arguments.of("test123", "ecd71870d1963316a97e3ac3408c9835ad8cf0f3c1bc703527c30265534f75ae", 64),
            Arguments.of("", "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", 64),
            Arguments.of("!@#$%^&*()", null, 64),
            Arguments.of(new String(new char[10000]).replace("\0", "a"), null, 64),
            Arguments.of("Hello 世界", null, 64)
        );
    }

    private static Stream<Arguments> base64TestData() {
        return Stream.of(
            Arguments.of("Hello World", "SGVsbG8gV29ybGQ="),
            Arguments.of("", ""),
            Arguments.of("!@#$%^&*()", "IUAjJCVeJiooKQ=="),
            Arguments.of("Hello 世界", "SGVsbG8g5LiW55WM")
        );
    }

    @ParameterizedTest
    @MethodSource("sha256TestData")
    void testHashSHA256(String input, String expectedHash, int expectedLength) throws HashUtil.HashGenerationException {
        String hash = HashUtil.hashSHA256(input);
        assertNotNull(hash);
        assertEquals(expectedLength, hash.length());
        if (expectedHash != null) {
            assertEquals(expectedHash, hash);
        }
    }

    @ParameterizedTest
    @MethodSource("base64TestData")
    void testBase64EncodeString(String input, String expectedEncoding) {
        String encoded = HashUtil.base64EncodeString(input);
        assertEquals(expectedEncoding, encoded);
    }

    @Test
    void testHashSHA256Exception() {
        try (MockedStatic<MessageDigest> mockMessageDiggest = mockStatic(MessageDigest.class)) {
            mockMessageDiggest.when(() -> MessageDigest.getInstance("SHA-256")).thenThrow(new NoSuchAlgorithmException());

            assertThrows(HashGenerationException.class, () -> HashUtil.hashSHA256(""));
        }
    }
}