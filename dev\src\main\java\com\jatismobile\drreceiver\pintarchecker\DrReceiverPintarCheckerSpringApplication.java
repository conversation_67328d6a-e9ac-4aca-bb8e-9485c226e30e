package com.jatismobile.drreceiver.pintarchecker;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;

import com.jatismobile.drreceiver.pintarchecker.configuration.ExceptionConfig.HolidayException;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

@SpringBootApplication
@EnableAutoConfiguration(exclude = { MongoAutoConfiguration.class })
public class DrReceiverPintarCheckerSpringApplication {
	public static void main(String[] args) {
        try {
            SpringApplication.run(DrReceiverPintarCheckerSpringApplication.class, args);
        } catch (HolidayException e) {
            AppLogUtil.writeInfoLog(null, "Today is holiday. Aborting startup.");
            // Exit silently without stack trace
            Runtime.getRuntime().exit(1);
        }
    }
}