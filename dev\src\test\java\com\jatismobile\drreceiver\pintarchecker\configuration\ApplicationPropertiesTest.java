package com.jatismobile.drreceiver.pintarchecker.configuration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.test.util.ReflectionTestUtils;

class ApplicationPropertiesTest {

    private ApplicationProperties applicationProperties;

    @BeforeEach
    void setUp() {
        applicationProperties = new ApplicationProperties();
    }

    @Test
    void testGetterAndSetterMethods() {
        String appName = "TestApp";
        String lookupType = "TestLookup";
        String serverTimezone = "UTC";

        applicationProperties.setAppName(appName);
        applicationProperties.setLookupType(lookupType);
        applicationProperties.setServerTimezone(serverTimezone);

        assertEquals(appName, applicationProperties.getAppName());
        assertEquals(lookupType, applicationProperties.getLookupType());
        assertEquals(serverTimezone, applicationProperties.getServerTimezone());
    }

    @Test
    void testOnStartupEventListener() {
        String appName = "TestApp";
        ReflectionTestUtils.setField(applicationProperties, "appName", appName);
        
        ApplicationReadyEvent mockEvent = mock(ApplicationReadyEvent.class);
        applicationProperties.onStartup(mockEvent);
        
        assertNotNull(applicationProperties.getAppName());
        assertEquals(appName, applicationProperties.getAppName());
    }

    @Test
    void testConfigurationPropertiesAnnotation() {
        ConfigurationProperties annotation = ApplicationProperties.class.getAnnotation(ConfigurationProperties.class);
        assertNotNull(annotation);
        assertEquals("app.config", annotation.prefix());
    }
}
