package com.jatismobile.drreceiver.pintarchecker.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.lang.reflect.Constructor;

class ThreadUtilTest {

    @Test
    void testSleepPositiveDuration() throws InterruptedException {
        long startTime = System.currentTimeMillis();
        ThreadUtil.sleep(100);
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        assertTrue(duration >= 100, "Sleep duration should be at least 100ms");
    }

    @Test
    void testSleepZeroDuration() throws InterruptedException {
        long startTime = System.currentTimeMillis();
        ThreadUtil.sleep(0);
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        assertTrue(duration >= 0, "Sleep duration should be non-negative");
    }

    @Test
    void testSleepInterruption() {
        Thread testThread = new Thread(() -> {
            try {
                ThreadUtil.sleep(1000);
                fail("Sleep should have been interrupted");
            } catch (InterruptedException e) {
                // Expected exception
            }
        });

        testThread.start();
        testThread.interrupt();
        try {
            testThread.join();
        } catch (InterruptedException e) {
            fail("Test thread join was interrupted");
        }
    }

    @Test
    void testPrivateConstructor() throws Exception {
        Constructor<ThreadUtil> constructor = ThreadUtil.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        constructor.newInstance();
        assertTrue(true, "Constructor should be accessible");
    }

    @Test
    void testSleepAfterSendWithValidParams() {
        String jobId = "test-job-123";
        Integer sleepTime = 50;
        ThreadUtil.sleepAfterSend(jobId, sleepTime);
        assertTrue(true, "Method should complete without exceptions");
    }

    @Test
    void testSleepAfterSendWithZeroSleepTime() {
        String jobId = "test-job-456";
        Integer sleepTime = 0;
        ThreadUtil.sleepAfterSend(jobId, sleepTime);
        assertTrue(true, "Method should complete without exceptions for zero sleep time");
    }

    @Test
    void testSleepAfterSendWithInterruption() {
        String jobId = "test-job-789";
        Integer sleepTime = 1000;

        Thread testThread = new Thread(() -> {
            ThreadUtil.sleepAfterSend(jobId, sleepTime);
        });

        testThread.start();
        testThread.interrupt();

        try {
            testThread.join();
        } catch (InterruptedException e) {
            fail("Test thread join was interrupted");
        }
    }

    @Test
    void testSleepAfterSendWithNullJobId() {
        Integer sleepTime = 50;
        assertDoesNotThrow(() -> ThreadUtil.sleepAfterSend(null, sleepTime));
    }

    @Test
    void testSleepAfterSendWithNullSleepTime() {
        String jobId = "test-job-null";
        assertThrows(NullPointerException.class, () -> ThreadUtil.sleepAfterSend(jobId, null));
    }
}