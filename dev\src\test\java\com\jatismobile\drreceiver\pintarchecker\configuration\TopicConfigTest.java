package com.jatismobile.drreceiver.pintarchecker.configuration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;

import org.apache.activemq.ActiveMQConnectionFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.autoconfigure.jms.DefaultJmsListenerContainerFactoryConfigurer;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.test.util.ReflectionTestUtils;

import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

@ExtendWith(MockitoExtension.class)
class TopicConfigTest {

    @Mock
    private MyGlobalConfig myGlobalConfig;

    @Mock
    private DefaultJmsListenerContainerFactoryConfigurer configurer;

    @Mock
    private ApplicationReadyEvent applicationReadyEvent;

    @InjectMocks
    private TopicConfig topicConfig;

    private Map<String, String> configMap;

    @BeforeEach
    void setUp() {
        configMap = new HashMap<>();
        configMap.put(ConfigKey.ACTIVEMQ_TOPIC_HOST, "tcp://localhost:61616");
        configMap.put(ConfigKey.ACTIVEMQ_TOPIC_USERNAME, "admin");
        configMap.put(ConfigKey.ACTIVEMQ_TOPIC_PASSWORD, "admin");
        ReflectionTestUtils.setField(topicConfig, "appName", "TestApp");
    }

    @Test
    void testReceiverActiveMQConnectionFactory() {
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        ActiveMQConnectionFactory factory = topicConfig.receiverActiveMQConnectionFactory();
        
        assertNotNull(factory);
        assertEquals("tcp://localhost:61616", factory.getBrokerURL());
        assertEquals("admin", factory.getUserName());
        assertEquals("admin", factory.getPassword());
    }

    @Test
    void testJmsTopicListenerContainerFactory() {
        ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory();
        
        DefaultJmsListenerContainerFactory result = topicConfig.jmsTopicListenerContainerFactory(
                configurer, connectionFactory);
        
        assertNotNull(result);
        verify(configurer).configure(any(DefaultJmsListenerContainerFactory.class), 
                any(ActiveMQConnectionFactory.class));
    }

    @Test
    void testReceiverActiveMQConnectionFactoryWithEmptyConfig() {
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(new HashMap<>());
        
        assertThrows(NullPointerException.class, () -> topicConfig.receiverActiveMQConnectionFactory());
    }

    @Test
    void testReceiverActiveMQConnectionFactoryWithNullConfig() {
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(null);
        
        assertThrows(NullPointerException.class, () -> {
            topicConfig.receiverActiveMQConnectionFactory();
        });
    }
    
    @Test
    void testStartUp(){
        try (MockedStatic<AppLogUtil> mockAppLogUtil = Mockito.mockStatic(AppLogUtil.class)) {
            
            topicConfig.onStartup(null);
            
            mockAppLogUtil.verify(() -> AppLogUtil.writeInfoLog(null, "[TestApp] | TopicConfig is ready."), times(1));
        }
    }
}
