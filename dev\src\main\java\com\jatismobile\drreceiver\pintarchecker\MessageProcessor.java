package com.jatismobile.drreceiver.pintarchecker;

import com.jatismobile.agais.lib.AgaisConstants;
import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.dao.MongoDao;
import com.jatismobile.drreceiver.pintarchecker.queue.AgaisQueue;
import com.jatismobile.drreceiver.pintarchecker.queue.QueueProducer;
import com.jatismobile.drreceiver.pintarchecker.service.HttpRequestService;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.QueryUtil;
import com.jatismobile.drreceiver.pintarchecker.utils.QueueKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

@Service
public class MessageProcessor {
	@Value("${custom.app.name}")
	private String appName;

	private final AgaisQueue agaisQueue;
	private final QueueProducer queueProducer;
	private final HttpRequestService httpRequestService;
	private final MyGlobalConfig myGlobalConfig;
	private final MongoDao mongoDao;

	private static final String ENCODE = "UTF-8";

	@Autowired
	public MessageProcessor(AgaisQueue agaisQueue,
			QueueProducer queueProducer,
			HttpRequestService httpRequestService,
			MyGlobalConfig myGlobalConfig,
			MongoDao mongoDao) {
		this.agaisQueue = agaisQueue;
		this.queueProducer = queueProducer;
		this.httpRequestService = httpRequestService;
		this.myGlobalConfig = myGlobalConfig;
		this.mongoDao = mongoDao;
	}

	@PostConstruct
	public void onApplicationStart() {

	}

	@JmsListener(containerFactory = "jmsListenerContainerFactory", 
		destination = "#{myGlobalConfig.getLookupConfigMap().get(T(com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey).ACTIVEMQ_TXQUE)}")
	public void receivedQueueMessage(String queueMessage) {
		String messageId = "";
		Map<String, String> msgFromQueue = new HashMap<>();
		Map<String, String> msgFromMongo = new HashMap<>();


		try {
			if (null == queueMessage || queueMessage.isEmpty()) {
				return;
			}

			msgFromQueue = QueryUtil.parseQuery(queueMessage);

			messageId = msgFromQueue.get(QueueKey.MESSAGE_ID);

			//validari messageId dari queue
			if (messageId.isEmpty() || messageId == null){
				AppLogUtil.writeInfoLog("", "[INFO] Queue message ignored - message_id not found or invalid - Message Queue: " + queueMessage);
				return;
			}

			// find data mongo
			String queueMessageFromMongo = findMongoProviderTrxId(messageId);
			if (queueMessageFromMongo.isEmpty()){
				AppLogUtil.writeInfoLog(messageId, "[INFO] Data temp tidak ditemukan - message ignored - Message Queue : "+ queueMessage);
				return;
			}

			msgFromMongo = QueryUtil.parseQuery(queueMessageFromMongo);
			String keyReroute = msgFromMongo.get("providerid")+"-"+msgFromMongo.get("channel_mq_name");
			AppLogUtil.writeInfoLog(messageId, "[INFO] find reroute by providerId "+msgFromMongo.get("providerid")+" and channel_mq_name "+msgFromMongo.get("channel_mq_name")+" from queue_message in mongodb.");
			// pencarian mapping reroute direct
			String mqName = myGlobalConfig.getRerouteHashMap().get(keyReroute);
			if (mqName != null && !mqName.isEmpty()){
				//jika di temukan, send queue ke transmitter
				queueProducer.sendMessage(messageId, mqName, queueMessageFromMongo, 4);
				AppLogUtil.writeInfoLog(messageId, "[INFO] Queue sent to transmitter from origin "+msgFromMongo.get("channel_mq_name")+" to reroute "+mqName);
			} else {
				//jika tidak di temukan, send queue ke DR Real Handler
				AppLogUtil.writeInfoLog(messageId, "[INFO] Mapping reroute direct not found - message sent to DR Real Handler.");
				String queueDrRealHandler = generateQueue(
						msgFromQueue.get(QueueKey.TRANSACTION_STATUS),
						msgFromQueue.get(QueueKey.MESSAGE_ID),
						msgFromQueue.get(QueueKey.PROVIDER_ID),
						msgFromQueue.get(QueueKey.PROVIDER_STATUS),
						msgFromQueue.get(QueueKey.DATE_RECEIVED),
						msgFromQueue.get(QueueKey.PROVIDER_NAME),
						msgFromQueue.get(QueueKey.WITHOUT_DR)
				);
				queueProducer.sendMessage(messageId, myGlobalConfig.getLookupConfigMap().get(ConfigKey.ACTIVEMQ_DR_REAL_HANDLER), queueDrRealHandler, 4);
				AppLogUtil.writeInfoLog(messageId, "[INFO] Queue sent to DR Real Handler - reroute mapping not found.");
			}




		} catch (UnsupportedEncodingException ex) {
			AppLogUtil.writeErrorLog("",
					"[receivedQueueMessage] [QMSGERR] Error in parsing or encoding charset message:" + queueMessage,
					ex);
			// sending to queue notif, not sending an email anymore
			agaisQueue.sendError("", AgaisConstants.ErrorId.QueueReadError,
					"[MessageProcessor][receivedQueueMessage] [QMSGERR] Error in parsing or encoding charset caused by "
							+ ex.getMessage());
		} catch (Exception e) {
			AppLogUtil.writeErrorLog("",
					"[receivedQueueMessage] get another exception in : ", e);
			agaisQueue.sendError("", AgaisConstants.ErrorId.UnknownError,
						"[MessageProcessor][receivedQueueMessage] get another exception in caused by "
								+ e.getMessage());
		}
	}


	private String findMongoProviderTrxId(String messageId) {
		String queueMessage = "";
		try {
			queueMessage = mongoDao.findProviderTrxId(myGlobalConfig.getLookupConfigMap().get(ConfigKey.MONGODB_DATABASE),
					myGlobalConfig.getLookupConfigMap().get(ConfigKey.MONGODB_TRX_COLLECTION),
					messageId
			);
			return queueMessage;
		} catch (Exception e) {
			AppLogUtil.writeErrorLog(messageId, "[receivedQueueMessage] failed get data mongo", e);
		}
		return queueMessage;
	}

	private String cleanStr(String input) {
		if (null == input) {
			input = "";
		} else {
			try {
				input = URLEncoder.encode(input, ENCODE);
			} catch (UnsupportedEncodingException e) {
				AppLogUtil.writeErrorLog("","error clean string caused by : ", e);
			}
		}
		return input;
	}

	private String generateQueue(String txStatus, String transid, String providerid, String status, String dtReceived,
								 String providerName, String withoutDr) {
		return "trxid=&transmitter_status=" + this.cleanStr(txStatus) + "&transmitter_sent=&provider_trxid="
				+ this.cleanStr(transid) + "&provider_id=" + this.cleanStr(providerid) + "&provider_status=" + status
				+ "&provider_status_received=" + dtReceived + "&providerName=" + providerName + "&withoutDr="
				+ withoutDr;
	}

}
