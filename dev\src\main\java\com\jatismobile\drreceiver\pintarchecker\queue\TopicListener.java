package com.jatismobile.drreceiver.pintarchecker.queue;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;

import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

@Component
public class TopicListener {
	@Value("${custom.app.name}")
	private String appName;

	@Value("${app.config.activemq.topic.reload.all}")
	private String reloadAll;
	@Value("${app.config.activemq.topic.reload.channels}")
	private String reloadChannelsConfig;
	@Value("${app.config.activemq.topic.reload.providers}")
	private String reloadProvidersConfig;

	private final MyGlobalConfig myGlobalConfig;

	@Autowired
	public TopicListener(MyGlobalConfig myGlobalConfig) {
		this.myGlobalConfig = myGlobalConfig;
	}

	@EventListener
	public void onStartup(ApplicationReadyEvent event) {
		AppLogUtil.writeInfoLog(null, "[" + appName + "] | " + getClass().getSimpleName() + " is ready.");
	}

	@JmsListener(
		containerFactory = "jmsTopicListenerContainerFactory", 
		destination = "#{myGlobalConfig.getLookupConfigMap().get(T(com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey).ACTIVEMQ_TOPIC)}"
	)
	public void receiveMessageFromTopic(String message) {
		AppLogUtil.writeInfoLog(null, "[TopicListener] Topic command : " + message);
		try {
			if (message.equalsIgnoreCase(reloadAll)) {
				myGlobalConfig.initConfig();
			} else {
				AppLogUtil.writeInfoLog(null, "[TopicListener] Undefined command");
				return;
			}
			AppLogUtil.writeInfoLog(null, "[TopicListener] Success reload app");
		} catch (Exception e) {
			AppLogUtil.writeErrorLog(null, "[TopicListener] Error reload app : ", e);
		}
	}
}
