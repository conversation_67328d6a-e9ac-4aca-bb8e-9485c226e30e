package com.jatismobile.drreceiver.pintarchecker.entity.mysql.appsconfig;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Data
@Entity
@Setter
@Getter
@ToString
@IdClass(JnsConfig.TypeNameKey.class)
@Table(name = "jns_config")
public class JnsConfig {
	@Id
    @Column(name = "lookup_type")
    private String lookupType;
	@Id
    @Column(name = "lookup_name")
    private String lookupName;
    @Column(name = "lookup_value")
    private String lookupValue;

	@Data
	public static class TypeNameKey implements Serializable {
		@Column(name = "lookup_type")
		private String lookupType;
		@Column(name = "lookup_name")
		private String lookupName;
	}

}
