package com.jatismobile.drreceiver.pintarchecker.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class HttpResultTest {

    @Test
    void testDefaultConstructor() {
        HttpResult result = new HttpResult();
        assertFalse(result.isSuccess());
        assertTrue(result.isCanRetry());
        assertTrue(result.isHasError());
        assertEquals(0, result.getStatusCode());
        assertNull(result.getResponse());
    }

    @Test
    void testAllArgsConstructor() {
        HttpResult result = new HttpResult(true, false, false, 200, "Success Response");
        assertTrue(result.isSuccess());
        assertFalse(result.isCanRetry());
        assertFalse(result.isHasError());
        assertEquals(200, result.getStatusCode());
        assertEquals("Success Response", result.getResponse());
    }

    @Test
    void testSettersAndGetters() {
        HttpResult result = new HttpResult();
        
        result.setSuccess(true);
        result.setCanRetry(false);
        result.setHasError(false);
        result.setStatusCode(200);
        result.setResponse("Success Response");

        assertTrue(result.isSuccess());
        assertFalse(result.isCanRetry());
        assertFalse(result.isHasError());
        assertEquals(200, result.getStatusCode());
        assertEquals("Success Response", result.getResponse());
    }

    @Test
    void testNegativeStatusCode() {
        HttpResult result = new HttpResult();
        result.setStatusCode(-1);
        assertEquals(-1, result.getStatusCode());
    }

    @Test
    void testEmptyStrings() {
        HttpResult result = new HttpResult();
        result.setResponse("");
        
        assertEquals("", result.getResponse());
    }

    @Test
    void testToStringFormat() {
        HttpResult result = new HttpResult();
        result.setSuccess(true);
        result.setStatusCode(200);
        result.setResponse("Test Response");
        
        String toString = result.toString();
        assertTrue(toString.contains("success=true"));
        assertTrue(toString.contains("statusCode=200"));
        assertTrue(toString.contains("response=Test Response"));
    }

    @Test
    void testEqualsWithSameValues() {
        HttpResult result1 = new HttpResult();
        result1.setSuccess(true);
        result1.setCanRetry(false);
        result1.setHasError(false);
        result1.setStatusCode(200);
        result1.setResponse("Test");

        HttpResult result2 = new HttpResult();
        result2.setSuccess(true);
        result2.setCanRetry(false);
        result2.setHasError(false);
        result2.setStatusCode(200);
        result2.setResponse("Test");

        assertEquals(result1, result2);
        assertEquals(result1.hashCode(), result2.hashCode());
    }

    @Test
    void testEqualsWithDifferentValues() {
        HttpResult result1 = new HttpResult();
        result1.setSuccess(true);
        result1.setCanRetry(false);
        result1.setHasError(false);
        result1.setStatusCode(200);
        result1.setResponse("Test");

        HttpResult result2 = new HttpResult();
        result2.setSuccess(true);
        result2.setCanRetry(false);
        result2.setHasError(false);
        result2.setStatusCode(404);
        result2.setResponse("Test");

        assertNotEquals(result1, result2);
        assertNotEquals(result1.hashCode(), result2.hashCode());
    }

    @Test
    void testEqualsWithNull() {
        HttpResult result = new HttpResult();
        assertNotEquals(null, result);
    }

    @Test
    void testEqualsWithDifferentClass() {
        HttpResult result = new HttpResult();
        assertNotEquals(result, new Object());
    }
    

    @Test
    void testToString() {
        HttpResult result2 = new HttpResult();
        result2.setSuccess(true);
        result2.setCanRetry(false);
        result2.setHasError(false);
        result2.setStatusCode(404);
        result2.setResponse("Test");

        // Validate toString format
        String expected = "HttpResult(success=true, canRetry=false, hasError=false, statusCode=404, response=Test)";
        assertEquals(expected, result2.toString());
    }
}