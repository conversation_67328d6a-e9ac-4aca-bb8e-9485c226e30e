package com.jatismobile.drreceiver.pintarchecker.entity.mysql.appsconfig;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.Test;

class JnsConfigTest {

    @Test
    void testJnsConfigGetterSetter() {
        JnsConfig jnsConfig = new JnsConfig();
        jnsConfig.setLookupType("TEST_TYPE");
        jnsConfig.setLookupName("TEST_NAME");
        jnsConfig.setLookupValue("TEST_VALUE");

        assertEquals("TEST_TYPE", jnsConfig.getLookupType());
        assertEquals("TEST_NAME", jnsConfig.getLookupName());
        assertEquals("TEST_VALUE", jnsConfig.getLookupValue());
    }

    @Test
    void testTypeNameKeyClass() {
        JnsConfig.TypeNameKey typeNameKey = new JnsConfig.TypeNameKey();
        typeNameKey.setLookupType("TYPE1");
        typeNameKey.setLookupName("NAME1");

        assertEquals("TYPE1", typeNameKey.getLookupType());
        assertEquals("NAME1", typeNameKey.getLookupName());
    }

    @Test
    void testToString() {
        JnsConfig jnsConfig = new JnsConfig();
        jnsConfig.setLookupType("TYPE");
        jnsConfig.setLookupName("NAME");
        jnsConfig.setLookupValue("VALUE");

        String toString = jnsConfig.toString();
        assertTrue(toString.contains("lookupType=TYPE"));
        assertTrue(toString.contains("lookupName=NAME"));
        assertTrue(toString.contains("lookupValue=VALUE"));
    }

    @Test
    void testEqualsAndHashCode() {
        JnsConfig config1 = new JnsConfig();
        config1.setLookupType("TYPE");
        config1.setLookupName("NAME");
        config1.setLookupValue("VALUE");

        JnsConfig config2 = new JnsConfig();
        config2.setLookupType("TYPE");
        config2.setLookupName("NAME");
        config2.setLookupValue("VALUE");

        assertEquals(config1, config2);
        assertEquals(config1.hashCode(), config2.hashCode());
    }

    @Test
    void testNullValues() {
        JnsConfig jnsConfig = new JnsConfig();
        assertNull(jnsConfig.getLookupType());
        assertNull(jnsConfig.getLookupName());
        assertNull(jnsConfig.getLookupValue());
    }
}
