package com.jatismobile.drreceiver.pintarchecker.utils;

public class ConfigKey {
    public static final String ACTIVEMQ_HOST = "activemq_host";
    public static final String ACTIVEMQ_NOTIFSENDER = "activemq_notifsender";
    public static final String ACTIVEMQ_TOPIC = "activemq_topic";
    public static final String ACTIVEMQ_TOPIC_HOST = "activemq_topic_host";
    public static final String ACTIVEMQ_TXACK = "activemq_txack";
    public static final String ACTIVEMQ_TXQUE = "activemq_txque";
    public static final String ACTIVEMQ_DR_REAL_HANDLER = "activemq_dr_real_handler";
    public static final String ACTIVEMQ_USERNAME = "activemq_username";
    public static final String ACTIVEMQ_PASSWORD = "activemq_password";
    public static final String ACTIVEMQ_TOPIC_USERNAME = "activemq_topic_username";
    public static final String ACTIVEMQ_TOPIC_PASSWORD = "activemq_topic_password";
    public static final String AGAIS_APPLICATION_ID = "agais_application_id";
    public static final String CALLBACK_URL = "callbackUrl";
    public static final String CHANNEL_NAME = "channel_name";
    public static final String DB_HOST = "db_host";
    public static final String DB_NAME = "db_name";
    public static final String DB_PASSWORD = "db_password";
    public static final String DB_PORT = "db_port";
    public static final String DB_TABLE = "db_table";
    public static final String DB_TABLE_HOLIDAY = "db_tableHoliday";
    public static final String DB_USER = "db_user";
    public static final String ENCODING = "encoding";
    public static final String ERR_EXPIRED = "ERR_EXPIRED";
    public static final String FAILED_SUBMIT = "FAILED_SUBMIT";
    public static final String MAIL_AUTH = "mail_auth";
    public static final String MAIL_FROM = "mail_from";
    public static final String MAIL_HOST = "mail_host";
    public static final String MAIL_MESSAGE = "mail_message";
    public static final String MAIL_PASS = "mail_pass";
    public static final String MAIL_PORT = "mail_port";
    public static final String MAIL_START_TLS = "mail_start_tls";
    public static final String MAIL_SUBJECT = "mail_subject";
    public static final String MAIL_TO = "mail_to";
    public static final String MAIL_USER = "mail_user";
    public static final String MAX_RETRY = "max_retry";
    public static final String MONGO_COLLECTION = "mongo_collection";
    public static final String MONGO_DBNAME = "mongo_dbname";
    public static final String MONGO_HOST = "mongo_host";
    public static final String MONGO_PASSWORD = "mongo_password";
    public static final String MONGO_PORT = "mongo_port";
    public static final String MONGO_USER = "mongo_user";
    public static final String PROVIDER_HASHDR = "provider_hasdr";
    public static final String PROVIDER_HOST = "provider_host";
    public static final String QUEUE_PERSISTENCY = "queue_persistency";
    public static final String RETRY_EXCEPTION = "retry_exception";
    public static final String RETRY_SCHEDULE_TIME = "retry_schedule_time";
    public static final String RETRY_SERVICE_TYPE = "retry_service_type";
    public static final String RETRY_TIMEOUT = "RETRY_TIMEOUT";
    public static final String SLEEP_AFTER_SEND = "sleep_after_send";
    public static final String STRING_SPLITTER = "string_splitter";
    public static final String SUBMIT_UNKNOWN = "SUBMIT_UNKNOWN";
    public static final String SUCCESS_NOT_RECEIVED = "SUCCESS_NOT_RECEIVED";
    public static final String SUCCESS_RECEIVED = "SUCCESS_RECEIVED";
    public static final String SUCCESS_WITH_ERROR = "SUCCESS_WITH_ERROR";
    public static final String TIMEOUT = "timeout";
    public static final String TOKEN = "token";
    public static final String UID = "uid";
    public static final String PWD = "pwd";
    public static final String LOGIN_URL = "login_url";
    public static final String EMAIL = "email";
    public static final String PASSWORD = "password";
    public static final String THRESHOLD_TOKEN_EXPIRED_MINUTES  = "threshold_token_expired_minutes";
    public static final String MONGODB_HOST  = "mongodb_host";
    public static final String MONGODB_PORT  = "mongodb_port";
    public static final String MONGODB_USERNAME  = "mongodb_username";
    public static final String MONGODB_PASSWORD  = "mongodb_password";
    public static final String MONGODB_DATABASE  = "mongodb_database";
    public static final String MONGODB_AUTH_DATABASE  = "mongodb_auth_database";
    public static final String MONGODB_TRX_COLLECTION  = "mongodb_trx_collection";

    private ConfigKey(){}
}
