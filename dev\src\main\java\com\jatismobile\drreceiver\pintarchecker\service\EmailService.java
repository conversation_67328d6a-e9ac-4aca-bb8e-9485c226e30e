package com.jatismobile.drreceiver.pintarchecker.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;

@Service
@EnableAsync
public class EmailService {

    private final JavaMailSender javaMailSender;
    private final MyGlobalConfig myGlobalConfig;

    @Autowired
    public EmailService(JavaMailSender javaMailSender, MyGlobalConfig myGlobalConfig) {
        this.javaMailSender = javaMailSender;
        this.myGlobalConfig = myGlobalConfig;
    }

    private void sendMail(String[] toEmail, String subject, String message) {

        SimpleMailMessage mailMessage = new SimpleMailMessage();

        mailMessage.setTo(toEmail);
        mailMessage.setSubject(subject);
        mailMessage.setText(message);

        javaMailSender.send(mailMessage);
    }

    @Async
    public void send(String message, Exception ex) {
        sendMail(myGlobalConfig.getLookupConfigMap().get(ConfigKey.MAIL_TO).split(","),
                myGlobalConfig.getLookupConfigMap().get(ConfigKey.MAIL_SUBJECT), String.format("%s%n%s%n%s",
                        myGlobalConfig.getLookupConfigMap().get(ConfigKey.MAIL_MESSAGE), message, ex.getMessage()));
    }
}
