package com.jatismobile.drreceiver.pintarchecker.utils.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AmqLogUtil {
	private static final Logger log = LoggerFactory.getLogger(AmqLogUtil.class);

	private AmqLogUtil(){}

	public static void writeInfoLog(String guid, String message) {
		String msg = message;
		if(null==guid){
			log.info(msg);
		}else {
			log.info("{} | {}", guid, msg);
		}
	}

	public static void writeErrorLog(String guid, String message, Exception ex) {
		AmqErrorLogUtil.writeErrorLog(guid, message, ex);
	}
}
