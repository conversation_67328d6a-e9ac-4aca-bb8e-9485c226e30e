package com.jatismobile.drreceiver.pintarchecker.utils.log;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.slf4j.Logger;

class AmqErrorLogUtilTest extends TestLogUtil {

    @Test
    void writeErrorLogWithUUID() throws Exception {
        String uuid = "test-uuid";
        String message = "Test error message";
        Exception exception = new RuntimeException("Test exception");
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AmqErrorLogUtil.class.getDeclaredField("log"), mockLogger);
        
        AmqErrorLogUtil.writeErrorLog(uuid, message, exception);
        
        Mockito.verify(mockLogger).error("{} | {} caused by: {}", uuid, message, exception);
    }

    @Test
    void writeErrorLogWithoutUUID() throws Exception {
        String message = "Test error message";
        Exception exception = new RuntimeException("Test exception");
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AmqErrorLogUtil.class.getDeclaredField("log"), mockLogger);
        
        AmqErrorLogUtil.writeErrorLog(null, message, exception);
            
        Mockito.verify(mockLogger).error("{} caused by: {}", message, exception);
    }

    @Test
    void writeErrorLogWithEmptyMessage() throws Exception {
        String uuid = "test-uuid";
        String message = "";
        Exception exception = new RuntimeException("Test exception");
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AmqErrorLogUtil.class.getDeclaredField("log"), mockLogger);
            
        AmqErrorLogUtil.writeErrorLog(uuid, message, exception);
        
        Mockito.verify(mockLogger).error("{} | {} caused by: {}", uuid, message, exception);
    }

    @Test
    void writeErrorLogWithNullException() throws Exception {
        String uuid = "test-uuid";
        String message = "Test message";
        
        Logger mockLogger = Mockito.mock(Logger.class);
        Mockito.when(mockLogger.isInfoEnabled()).thenReturn(false);
        setFinalStatic(AmqErrorLogUtil.class.getDeclaredField("log"), mockLogger);
            
        AmqErrorLogUtil.writeErrorLog(uuid, message, null);
        
        Mockito.verify(mockLogger).error("{} | {} caused by: {}", uuid, message, null);
    }
}
