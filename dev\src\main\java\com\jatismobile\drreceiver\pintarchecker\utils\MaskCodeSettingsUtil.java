package com.jatismobile.drreceiver.pintarchecker.utils;

import java.util.List;

import com.jatismobile.drreceiver.pintarchecker.model.MaskCodeSettings;

public class MaskCodeSettingsUtil {

    private MaskCodeSettingsUtil() {
        // Utility class, no instances
    }

    /**
     * Finds a MaskCodeSettings object by its key.
     *
     * @param settingsList the list of MaskCodeSettings
     * @param key the key to search for
     * @return the found MaskCodeSettings object, or null if not found
     */
    public static MaskCodeSettings findByKey(List<MaskCodeSettings> settingsList, String key) {
        if (settingsList == null || key == null) {
            return null;
        }
        return settingsList.stream()
                .filter(setting -> key.equals(setting.getKey()))
                .findFirst()
                .orElse(null);
    }
}
