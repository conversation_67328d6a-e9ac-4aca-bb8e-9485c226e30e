package com.jatismobile.drreceiver.pintarchecker.configuration;

import static org.junit.jupiter.api.Assertions.*;

import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import javax.net.ssl.SSLSession;
import javax.net.ssl.X509TrustManager;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import okhttp3.OkHttpClient;

@ExtendWith(MockitoExtension.class)
class OkHttpClientConfigurationTest {

    @Mock
    private SSLSession mockSslSession;

    private final OkHttpClientConfiguration configuration = new OkHttpClientConfiguration();

    @Test
    void shouldCreateOkHttpClientWithCustomSSLConfiguration() throws NoSuchAlgorithmException, KeyManagementException {
        OkHttpClient client = configuration.okHttpClient();
        
        assertNotNull(client);
        assertNotNull(client.sslSocketFactory());
        assertNotNull(client.hostnameVerifier());
    }

    @Test
    void shouldAcceptAllHostnames() throws NoSuchAlgorithmException, KeyManagementException {
        OkHttpClient client = configuration.okHttpClient();
        
        assertTrue(client.hostnameVerifier().verify("any-hostname", mockSslSession));
        assertTrue(client.hostnameVerifier().verify("localhost", mockSslSession));
        assertTrue(client.hostnameVerifier().verify("", mockSslSession));
    }

    @Test
    void shouldHaveTrustManagerThatAcceptsAllCertificates() throws NoSuchAlgorithmException, KeyManagementException {
        OkHttpClient client = configuration.okHttpClient();
        X509TrustManager trustManager = (X509TrustManager) client.x509TrustManager();
        
        assertNotNull(trustManager);
        assertEquals(0, trustManager.getAcceptedIssuers().length);
        
        // Verify no exceptions are thrown when checking certificates
        assertDoesNotThrow(() -> {
            trustManager.checkClientTrusted(new X509Certificate[]{}, "RSA");
        });
        
        assertDoesNotThrow(() -> {
            trustManager.checkServerTrusted(new X509Certificate[]{}, "RSA");
        });
    }

    @Test
    void shouldCreateNewInstanceForEachCall() throws NoSuchAlgorithmException, KeyManagementException {
        OkHttpClient client1 = configuration.okHttpClient();
        OkHttpClient client2 = configuration.okHttpClient();
        
        assertNotNull(client1);
        assertNotNull(client2);
        assertNotSame(client1, client2);
    }
}
