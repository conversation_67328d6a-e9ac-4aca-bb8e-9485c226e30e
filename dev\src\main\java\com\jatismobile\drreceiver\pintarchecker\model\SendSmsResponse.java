package com.jatismobile.drreceiver.pintarchecker.model;

import com.google.gson.annotations.SerializedName;

@lombok.Setter
@lombok.Getter
@lombok.ToString
@lombok.EqualsAndHashCode
@lombok.AllArgsConstructor
@lombok.NoArgsConstructor
public class SendSmsResponse {
    @SerializedName("status")
    private int status;
    @SerializedName("code")
    private int code;
    @SerializedName("message")
    private String message;
    @SerializedName("data")
    private DataDetails data;

    @lombok.Setter
    @lombok.Getter
    @lombok.ToString
    @lombok.EqualsAndHashCode
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class DataDetails{
        @SerializedName("transaction_id")
        private int transactionId;
        @SerializedName("reference_id")
        private String referenceId;
        @SerializedName("customer_id")
        private String customerId;
        @SerializedName("otp_code")
        private String otpCode;
        @SerializedName("message")
        private String message;
        @SerializedName("status")
        private int status;
        @SerializedName("created_at")
        private String createdAt;
        @SerializedName("expires_at")
        private String expiresAt;
    }
}