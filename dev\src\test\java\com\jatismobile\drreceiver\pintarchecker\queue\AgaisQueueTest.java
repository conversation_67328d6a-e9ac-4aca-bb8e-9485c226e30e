package com.jatismobile.drreceiver.pintarchecker.queue;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
import com.jatismobile.drreceiver.pintarchecker.service.EmailService;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

@ExtendWith(MockitoExtension.class)
class AgaisQueueTest {

    @Mock
    private QueueProducer queueProducer;

    @Mock
    private EmailService emailService;

    @Mock
    private MyGlobalConfig myGlobalConfig;

    private AgaisQueue agaisQueue;
        
    private Map<String, String> configMap = new HashMap<>();

    @BeforeEach
    void setUp() {
        agaisQueue = new AgaisQueue(queueProducer, emailService, myGlobalConfig);
        ReflectionTestUtils.setField(agaisQueue, "appName", "TestApp");
        configMap.put(ConfigKey.ACTIVEMQ_NOTIFSENDER, "notifQueue");
        configMap.put(ConfigKey.AGAIS_APPLICATION_ID, "testAppId");
    }

    private static Stream<Arguments> sendErrorTestCases() {
        return Stream.of(
            Arguments.of("test-guid", 123, "Test error message", false),
            Arguments.of("test-guid", 123, "Test & error % message", false),
            Arguments.of("test-guid", 123, "", false)
        );
    }

    @ParameterizedTest
    @MethodSource("sendErrorTestCases")
    void sendErrorTest(String guid, Integer errorId, String message, boolean shouldThrowException) {
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
        if (shouldThrowException) {
            doThrow(new RuntimeException("Queue error"))
                .when(queueProducer)
                .sendMessage(any(), any(), any(), anyInt());

            agaisQueue.sendError(guid, errorId, message);
            verify(emailService, times(1)).send(anyString(), any(Exception.class));
        } else {
            agaisQueue.sendError(guid, errorId, message);
            verify(queueProducer, times(1)).sendMessage(
                anyString(),
                anyString(),
                anyString(),
                anyInt()
            );
        }
    }

    @Test
    void sendErrorHandlesException() {
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
        String guid = "test-guid";
        Integer errorId = 123;
        String message = "Test error message";

        doThrow(new RuntimeException("Queue error"))
            .when(queueProducer)
            .sendMessage(any(), any(), any(), anyInt());

        agaisQueue.sendError(guid, errorId, message);

        verify(emailService, times(1)).send(anyString(), any(Exception.class));
    }
    
    @Test
    void testStartUp(){
        try (MockedStatic<AppLogUtil> mockAppLogUtil = Mockito.mockStatic(AppLogUtil.class)) {
            
            agaisQueue.onStartup(null);
            
            mockAppLogUtil.verify(() -> AppLogUtil.writeInfoLog(null, "[TestApp] | AgaisQueue is ready."), times(1));
        }
    }
}