package com.jatismobile.drreceiver.pintarchecker.model;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class TokenRequestTest {

    @Test
    void testTokenRequestConstructorAndGetters() {
        TokenRequest request = new TokenRequest("<EMAIL>", "password123");
        assertEquals("<EMAIL>", request.getEmail());
        assertEquals("password123", request.getPassword());
    }

    @Test
    void testTokenRequestSetters() {
        TokenRequest request = new TokenRequest();
        request.setEmail("<EMAIL>");
        request.setPassword("newpassword");
        assertEquals("<EMAIL>", request.getEmail());
        assertEquals("newpassword", request.getPassword());
    }

    @Test
    void testTokenRequestToString() {
        TokenRequest request = new TokenRequest("<EMAIL>", "password123");
        String toString = request.toString();
        assertTrue(toString.contains("<EMAIL>"));
        assertTrue(toString.contains("password123"));
    }

    @Test
    void testTokenRequestWithNullValues() {
        TokenRequest request = new TokenRequest(null, null);
        assertNull(request.getEmail());
        assertNull(request.getPassword());
    }

    @Test
    void testTokenRequestWithEmptyValues() {
        TokenRequest request = new TokenRequest("", "");
        assertEquals("", request.getEmail());
        assertEquals("", request.getPassword());
    }
}
