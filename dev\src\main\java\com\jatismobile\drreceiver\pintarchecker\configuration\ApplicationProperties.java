package com.jatismobile.drreceiver.pintarchecker.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

@lombok.Getter
@lombok.Setter
@Component
@ConfigurationProperties(prefix = "app.config")
public class ApplicationProperties {
    @Value("${custom.app.name}")
    private String appName;
    private String lookupType;
    private String serverTimezone;

    @EventListener
    public void onStartup(ApplicationReadyEvent event) {
        AppLogUtil.writeInfoLog(null, "[" + appName + "] | " + getClass().getSimpleName().split("\\$")[0] + " is ready.");
    }
}
