package com.jatismobile.drreceiver.pintarchecker.configuration;

import org.apache.activemq.ActiveMQConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jms.DefaultJmsListenerContainerFactoryConfigurer;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;

import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

import javax.jms.ConnectionFactory;

@EnableJms
@Configuration
public class TopicConfig {
    @Value("${custom.app.name}")
    private String appName;

    private final MyGlobalConfig myGlobalConfig;

    @Autowired
    public TopicConfig(MyGlobalConfig myGlobalConfig) {
        this.myGlobalConfig = myGlobalConfig;
    }

    @Bean("topicReceiverFactory")
    public ActiveMQConnectionFactory receiverActiveMQConnectionFactory() {
        @SuppressWarnings("all")
        ActiveMQConnectionFactory activeMQConnectionFactory = new ActiveMQConnectionFactory();
        activeMQConnectionFactory.setBrokerURL(myGlobalConfig.getLookupConfigMap().get(ConfigKey.ACTIVEMQ_TOPIC_HOST));
        activeMQConnectionFactory.setUserName(myGlobalConfig.getLookupConfigMap().get(ConfigKey.ACTIVEMQ_TOPIC_PASSWORD));
        activeMQConnectionFactory.setPassword(myGlobalConfig.getLookupConfigMap().get(ConfigKey.ACTIVEMQ_TOPIC_USERNAME));
        return activeMQConnectionFactory;
    }

    @Bean
    public DefaultJmsListenerContainerFactory jmsTopicListenerContainerFactory(
            DefaultJmsListenerContainerFactoryConfigurer configurer,
            @Qualifier("topicReceiverFactory") ConnectionFactory connectionFactory) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        factory.setPubSubDomain(true);
        return factory;
    }

    @EventListener
    public void onStartup(ApplicationReadyEvent event) {
        AppLogUtil.writeInfoLog(null,
                "[" + appName + "] | " + getClass().getSimpleName().split("\\$")[0] + " is ready.");
    }
}
