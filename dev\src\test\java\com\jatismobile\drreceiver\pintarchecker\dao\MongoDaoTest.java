//package com.jatismobile.drreceiver.pintarchecker.dao;
//
//import static org.mockito.ArgumentMatchers.argThat;
//import static org.mockito.Mockito.*;
//
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//
//import org.bson.Document;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
//import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
//import com.mongodb.ConnectionString;
//import com.mongodb.client.MongoClient;
//import com.mongodb.client.MongoClients;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.MongoDatabase;
//
//@ExtendWith(MockitoExtension.class)
//class MongoDaoTest {
//
//    @Mock
//    private MyGlobalConfig myGlobalConfig;
//
//    @Mock
//    private MongoClient mongoClient;
//
//    @Mock
//    private MongoDatabase mongoDatabase;
//
//    @Mock
//    private MongoCollection<Document> mongoCollection;
//
//    private MongoDao mongoDao;
//    private Map<String, String> configMap;
//
//    @BeforeEach
//    void setUp() {
//        configMap = new HashMap<>();
//        configMap.put(ConfigKey.MONGODB_USERNAME, "testuser");
//        configMap.put(ConfigKey.MONGODB_PASSWORD, "testpass");
//        configMap.put(ConfigKey.MONGODB_HOST, "localhost");
//        configMap.put(ConfigKey.MONGODB_PORT, "27017");
//        configMap.put(ConfigKey.MONGODB_AUTH_DATABASE, "admin");
//
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//    }
//
//    @Test
//    void testInsertProviderTrxId() {
//        String jobId = "test-job";
//        String database = "testdb";
//        String collection = "testcollection";
//        String providerTrxId = "test-trx-123";
//        Date created = new Date();
//
//        String connection = String.format("mongodb://%s:%s@%s:%s/?authSource=%s",
//            configMap.get(ConfigKey.MONGODB_USERNAME),
//            configMap.get(ConfigKey.MONGODB_PASSWORD),
//            configMap.get(ConfigKey.MONGODB_HOST),
//			configMap.get(ConfigKey.MONGODB_PORT),
//			configMap.get(ConfigKey.MONGODB_AUTH_DATABASE)
//        );
//        ConnectionString connectionString = new ConnectionString(connection);
//
//        when(mongoClient.getDatabase(database)).thenReturn(mongoDatabase);
//        when(mongoDatabase.getCollection(collection)).thenReturn(mongoCollection);
//        try (MockedStatic<MongoClients> clientMock = mockStatic(MongoClients.class)) {
//            clientMock.when(() -> MongoClients.create(connectionString)).thenReturn(mongoClient);
//
//            mongoDao = new MongoDao(myGlobalConfig);
//            mongoDao.insertProviderTrxId(jobId, database, collection, providerTrxId, created);
//
//            verify(mongoClient).getDatabase(database);
//            verify(mongoDatabase).getCollection(collection);
//            verify(mongoCollection).insertOne(argThat(document ->
//                document.get("provider_trx_id").equals(providerTrxId) &&
//                document.get("date_created").equals(created)
//            ));
//        }
//    }
//}
