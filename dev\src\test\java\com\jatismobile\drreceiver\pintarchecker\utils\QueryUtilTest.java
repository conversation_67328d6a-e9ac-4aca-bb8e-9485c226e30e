package com.jatismobile.drreceiver.pintarchecker.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

class QueryUtilTest {

    @Test
    void testParseQueryWithValidInput() throws UnsupportedEncodingException {
        String query = "name=John+Doe&age=25&city=New+York";
        Map<String, String> result = QueryUtil.parseQuery(query);
        
        assertEquals("<PERSON> Doe", result.get("name"));
        assertEquals("25", result.get("age"));
        assertEquals("New York", result.get("city"));
    }

    @Test
    void testParseQueryWithSpecialCharacters() throws UnsupportedEncodingException {
        String query = "email=test%40example.com&message=Hello%21+How+are+you%3F";
        Map<String, String> result = QueryUtil.parseQuery(query);
        
        assertEquals("<EMAIL>", result.get("email"));
        assertEquals("Hello! How are you?", result.get("message"));
    }

    @Test
    void testParseQueryWithInvalidFormat() {
        String query = "invalid&query=format";
        assertThrows(StringIndexOutOfBoundsException.class, () -> {
            QueryUtil.parseQuery(query);
        });
    }

    @Test
    void testQueryBuilderWithValidInput() throws UnsupportedEncodingException {
        Map<String, String> params = new HashMap<>();
        params.put("name", "John Doe");
        params.put("age", "25");
        params.put("city", "New York");
        
        String result = QueryUtil.queryBuilder(params);
        assertTrue(result.contains("name=John+Doe"));
        assertTrue(result.contains("age=25"));
        assertTrue(result.contains("city=New+York"));
        assertEquals(2, result.chars().filter(ch -> ch == '&').count());
    }

    @Test
    void testQueryBuilderWithSpecialCharacters() throws UnsupportedEncodingException {
        Map<String, String> params = new HashMap<>();
        params.put("email", "<EMAIL>");
        params.put("message", "Hello! How are you?");
        
        String result = QueryUtil.queryBuilder(params);
        assertTrue(result.contains("email=test%40example.com"));
        assertTrue(result.contains("message=Hello%21+How+are+you%3F"));
        assertEquals(1, result.chars().filter(ch -> ch == '&').count());
    }

    @Test
    void testQueryBuilderWithEmptyValue() throws UnsupportedEncodingException {
        Map<String, String> params = new HashMap<>();
        params.put("key1", "");
        params.put("key2", "value2");
        
        String result = QueryUtil.queryBuilder(params);
        assertTrue(result.contains("key1="));
        assertTrue(result.contains("key2=value2"));
    }

    @Test
    void testQueryBuilderWithNullValue() throws UnsupportedEncodingException {
        Map<String, String> params = new HashMap<>();
        params.put("key1", null);
        params.put("key2", "value2");
        
        String result = QueryUtil.queryBuilder(params);
        assertTrue(result.contains("key1="));
        assertTrue(result.contains("key2=value2"));
    }

    @Test
    void testQueryBuilderWithSingleParameter() throws UnsupportedEncodingException {
        Map<String, String> params = new HashMap<>();
        params.put("single", "value");
        
        String result = QueryUtil.queryBuilder(params);
        assertEquals("single=value", result);
        assertFalse(result.contains("&"));
    }
}