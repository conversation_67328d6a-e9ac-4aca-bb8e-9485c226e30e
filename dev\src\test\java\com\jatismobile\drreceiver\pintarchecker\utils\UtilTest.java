package com.jatismobile.drreceiver.pintarchecker.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;

class UtilTest {

    @Test
    void testParseIntWithValidNumber() {
        assertEquals(123, Util.parseInt("123"));
    }

    @Test
    void testParseIntWithZero() {
        assertEquals(0, Util.parseInt("0"));
    }

    @Test
    void testParseIntWithNegativeNumber() {
        assertEquals(-456, Util.parseInt("-456"));
    }

    @Test
    void testParseIntWithInvalidInput() {
        assertEquals(0, Util.parseInt("abc"));
    }

    @Test
    void testParseIntWithNull() {
        assertEquals(0, Util.parseInt(null));
    }

    @Test
    void testParseIntWithEmptyString() {
        assertEquals(0, Util.parseInt(""));
    }

    @Test
    void testParseIntWithWhitespace() {
        assertEquals(0, Util.parseInt(" "));
    }
}
