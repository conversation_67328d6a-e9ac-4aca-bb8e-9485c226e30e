package com.jatismobile.drreceiver.pintarchecker.model;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class SendSmsRequestTest {

    @Test
    void testSendSmsRequestGettersAndSetters() {
        SendSmsRequest request = new SendSmsRequest();
        
        request.setReferenceId("REF123");
        request.setCustomerId("CUST456");
        request.setOtpCode("1234");
        request.setMessage("Test Message");

        assertEquals("REF123", request.getReferenceId());
        assertEquals("CUST456", request.getCustomerId());
        assertEquals("1234", request.getOtpCode());
        assertEquals("Test Message", request.getMessage());
    }

    @Test
    void testSendSmsRequestAllArgsConstructor() {
        SendSmsRequest request = new SendSmsRequest("REF123", "CUST456", "1234", "Test Message");
        
        assertEquals("REF123", request.getReferenceId());
        assertEquals("CUST456", request.getCustomerId());
        assertEquals("1234", request.getOtpCode());
        assertEquals("Test Message", request.getMessage());
    }

    @Test
    void testSendSmsRequestEquality() {
        SendSmsRequest request1 = new SendSmsRequest("REF123", "CUST456", "1234", "Test Message");
        SendSmsRequest request2 = new SendSmsRequest("REF123", "CUST456", "1234", "Test Message");
        SendSmsRequest request3 = new SendSmsRequest("REF789", "CUST456", "1234", "Test Message");

        assertEquals(request1, request2);
        assertNotEquals(request1, request3);
        assertEquals(request1.hashCode(), request2.hashCode());
    }

    @Test
    void testSendSmsRequestToString() {
        SendSmsRequest request = new SendSmsRequest("REF123", "CUST456", "1234", "Test Message");
        String toString = request.toString();
        
        assertTrue(toString.contains("REF123"));
        assertTrue(toString.contains("CUST456"));
        assertTrue(toString.contains("1234"));
        assertTrue(toString.contains("Test Message"));
    }

    @Test
    void testSendSmsRequestWithNullValues() {
        SendSmsRequest request = new SendSmsRequest(null, null, null, null);
        
        assertNull(request.getReferenceId());
        assertNull(request.getCustomerId());
        assertNull(request.getOtpCode());
        assertNull(request.getMessage());
    }
}
