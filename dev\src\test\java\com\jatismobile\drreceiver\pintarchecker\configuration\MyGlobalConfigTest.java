//package com.jatismobile.drreceiver.pintarchecker.configuration;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.mockito.Mockito.*;
//
//import java.time.LocalDate;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import com.jatismobile.drreceiver.pintarchecker.entity.mysql.appsconfig.JnsConfig;
//import com.jatismobile.drreceiver.pintarchecker.repository.mysql.appsconfig.JnsConfigRepository;
//import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
//import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;
//
//@ExtendWith(MockitoExtension.class)
//class MyGlobalConfigTest {
//    private JnsConfigRepository jnsConfigRepository;
//    private ApplicationProperties applicationProperties;
//
//    private MyGlobalConfig myGlobalConfig;
//
//    private List<JnsConfig> jnsConfigs;
//
//    @BeforeEach
//    void setUp() {
//        jnsConfigRepository = mock(JnsConfigRepository.class);
//        applicationProperties = mock(ApplicationProperties.class);
//
//        JnsConfig config1 = new JnsConfig();
//        config1.setLookupName(ConfigKey.DB_HOST);
//        config1.setLookupValue("localhost");
//
//        JnsConfig config2 = new JnsConfig();
//        config2.setLookupName(ConfigKey.DB_PORT);
//        config2.setLookupValue("3306");
//
//        JnsConfig config3 = new JnsConfig();
//        config3.setLookupName(ConfigKey.DB_NAME);
//        config3.setLookupValue("testdb?useSSL=false");
//
//        JnsConfig config4 = new JnsConfig();
//        config4.setLookupName(ConfigKey.DB_USER);
//        config4.setLookupValue("testdb");
//
//        JnsConfig config5 = new JnsConfig();
//        config5.setLookupName(ConfigKey.DB_PASSWORD);
//        config5.setLookupValue("testdb");
//
//        JnsConfig config6 = new JnsConfig();
//        config6.setLookupName(ConfigKey.ACTIVEMQ_TXQUE);
//        config6.setLookupValue("activemq_txque");
//
//        JnsConfig config7 = new JnsConfig();
//        config7.setLookupName(ConfigKey.DB_TABLE_HOLIDAY);
//        config7.setLookupValue("holiday");
//
//        jnsConfigs = Arrays.asList(config1, config2, config3, config4, config5, config6, config7);
//
//        when(applicationProperties.getLookupType()).thenReturn("test_lookup");
//        when(applicationProperties.getServerTimezone()).thenReturn("UTC");
//
//        when(jnsConfigRepository.findByLookupType("test_lookup"))
//                .thenReturn(jnsConfigs);
//
//        myGlobalConfig = new MyGlobalConfig(jnsConfigRepository, applicationProperties);
//        ReflectionTestUtils.setField(myGlobalConfig, "appName", "TestApp");
//    }
//
//    @Test
//    void testInitLookupConfig() {
//        myGlobalConfig.initLookupConfig();
//
//        Map<String, String> lookupConfigMap = myGlobalConfig.getLookupConfigMap();
//        assertNotNull(lookupConfigMap);
//        assertEquals("localhost", lookupConfigMap.get(ConfigKey.DB_HOST));
//        assertEquals("3306", lookupConfigMap.get(ConfigKey.DB_PORT));
//        assertEquals("testdb?useSSL=false", lookupConfigMap.get(ConfigKey.DB_NAME));
//    }
//
//    @Test
//    void testInitLookupConfigWithDBNameNoSymbol() {
//        ArrayList<JnsConfig> lookupConfigs = new ArrayList<>(jnsConfigs);
//        JnsConfig config = new JnsConfig();
//        config.setLookupName(ConfigKey.DB_NAME);
//        config.setLookupValue("testdb");
//        lookupConfigs.add(config);
//
//        when(applicationProperties.getLookupType()).thenReturn("test_lookup");
//        when(applicationProperties.getServerTimezone()).thenReturn("UTC");
//
//        when(jnsConfigRepository.findByLookupType("test_lookup"))
//                .thenReturn(lookupConfigs);
//
//        myGlobalConfig = new MyGlobalConfig(jnsConfigRepository, applicationProperties);
//        ReflectionTestUtils.setField(myGlobalConfig, "appName", "TestApp");
//
//        myGlobalConfig.initLookupConfig();
//
//        Map<String, String> lookupConfigMap = myGlobalConfig.getLookupConfigMap();
//        assertNotNull(lookupConfigMap);
//        assertEquals("localhost", lookupConfigMap.get(ConfigKey.DB_HOST));
//        assertEquals("3306", lookupConfigMap.get(ConfigKey.DB_PORT));
//        assertEquals("testdb", lookupConfigMap.get(ConfigKey.DB_NAME));
//    }
//
//    @Test
//    void testIsHolidayForNonHolidayConfiguration() {
//        when(applicationProperties.getLookupType()).thenReturn("test_lookup");
//        assertFalse(myGlobalConfig.isHoliday());
//    }
//
//    @Test
//    void testProvidersHashMapOperations() {
//        Map<String, String> testMap = new HashMap<>();
//        testMap.put("provider1", "value1");
//        testMap.put("provider2", "value2");
//
//        myGlobalConfig.setProvidersHashMap(testMap);
//        Map<String, String> result = myGlobalConfig.getProvidersHashMap();
//
//        assertNotNull(result);
//        assertEquals(2, result.size());
//        assertEquals("value1", result.get("provider1"));
//        assertEquals("value2", result.get("provider2"));
//    }
//
//    @Test
//    void testChannelConfigMapOperations() {
//        Map<String, String> testMap = new HashMap<>();
//        testMap.put("channel1", "value1");
//        testMap.put("channel2", "value2");
//
//        myGlobalConfig.setChannelConfigMap(testMap);
//        Map<String, String> result = myGlobalConfig.getChannelConfigMap();
//
//        assertNotNull(result);
//        assertEquals(2, result.size());
//        assertEquals("value1", result.get("channel1"));
//        assertEquals("value2", result.get("channel2"));
//    }
//
//    @Test
//    void testLookupConfigMapOperations() {
//        Map<String, String> testMap = new HashMap<>();
//        testMap.put("lookup1", "value1");
//        testMap.put("lookup2", "value2");
//
//        myGlobalConfig.setLookupConfigMap(testMap);
//        Map<String, String> result = myGlobalConfig.getLookupConfigMap();
//
//        assertNotNull(result);
//        assertEquals(2, result.size());
//        assertEquals("value1", result.get("lookup1"));
//        assertEquals("value2", result.get("lookup2"));
//    }
//
//    @Test
//    void testStartUp(){
//        try (MockedStatic<AppLogUtil> mockAppLogUtil = Mockito.mockStatic(AppLogUtil.class)) {
//
//            myGlobalConfig.onStartup(null);
//
//            mockAppLogUtil.verify(() -> AppLogUtil.writeInfoLog(null, "[TestApp] | MyGlobalConfig is ready."), times(1));
//        }
//    }
//
//    @Test
//    void testIsHolidayForSunday() {
//        when(applicationProperties.getLookupType()).thenReturn("818");
//
//        LocalDate mockDate = LocalDate.of(2024, 1, 7); // A Sunday
//
//        try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class);
//             MockedStatic<HolidayDao> mockedHolidayDao = mockStatic(HolidayDao.class);
//             MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//
//            when(LocalDate.now()).thenReturn(mockDate);
//
//            assertTrue(myGlobalConfig.isHoliday());
//
//            mockedAppLogUtil.verify(() ->
//                AppLogUtil.writeInfoLog(eq(null), eq("[HolidayConfiguration] today is sunday ? true")));
//        }
//    }
//
//    @Test
//    void testIsHolidayForNonSundayHoliday() {
//        when(applicationProperties.getLookupType()).thenReturn("818");
//
//        LocalDate mockDate = LocalDate.of(2024, 1, 1); // A Monday holiday
//
//        try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class);
//             MockedStatic<HolidayDao> mockedHolidayDao = mockStatic(HolidayDao.class);
//             MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//            when(LocalDate.now()).thenReturn(mockDate);
//
//            when(HolidayDao.isHoliday("******************************************************************", "testdb", "testdb", "holiday", "2024-01-01"))
//                .thenReturn(true);
//
//            assertTrue(myGlobalConfig.isHoliday());
//
//            mockedAppLogUtil.verify(() ->
//                AppLogUtil.writeInfoLog(eq(null), eq("[HolidayConfiguration] today is holiday ? true")));
//        }
//    }
//
//    @Test
//    void testIsHolidayForRegularWorkday() {
//        when(applicationProperties.getLookupType()).thenReturn("818");
//
//        LocalDate mockDate = LocalDate.of(2024, 1, 2); // A Tuesday
//
//        try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class);
//             MockedStatic<HolidayDao> mockedHolidayDao = mockStatic(HolidayDao.class)) {
//            when(LocalDate.now()).thenReturn(mockDate);
//
//            when(HolidayDao.isHoliday("******************************************************************", "testdb", "testdb", "holiday", "2024-01-02"))
//                .thenReturn(false);
//
//            assertFalse(myGlobalConfig.isHoliday());
//        }
//    }
//
//    @Test
//    void testIsHolidayForBypassConfiguration() {
//        when(applicationProperties.getLookupType()).thenReturn("818_bypass");
//        LocalDate mockDate = LocalDate.of(2024, 1, 7); // Even though it's Sunday
//
//        try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
//            when(LocalDate.now()).thenReturn(mockDate);
//
//            assertFalse(myGlobalConfig.isHoliday());
//        }
//    }
//
//    @Test
//    void testIsHolidayForNon818Configuration() {
//        when(applicationProperties.getLookupType()).thenReturn("other_config");
//        LocalDate mockDate = LocalDate.of(2024, 1, 7); // Even though it's Sunday
//
//        try (MockedStatic<LocalDate> mockedLocalDate = mockStatic(LocalDate.class)) {
//            when(LocalDate.now()).thenReturn(mockDate);
//
//            assertFalse(myGlobalConfig.isHoliday());
//        }
//    }
//}