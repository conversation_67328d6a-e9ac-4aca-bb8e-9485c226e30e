package com.jatismobile.drreceiver.pintarchecker.configuration;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class ExceptionConfigTest {

    @Test
    void testHolidayExceptionMessage() {
        String errorMessage = "This is a holiday";
        ExceptionConfig.HolidayException exception = new ExceptionConfig.HolidayException(errorMessage);
        assertEquals(errorMessage, exception.getMessage());
    }

    @Test
    void testHolidayExceptionStackTrace() {
        ExceptionConfig.HolidayException exception = new ExceptionConfig.HolidayException("Test message");
        Throwable stackTrace = exception.fillInStackTrace();
        assertSame(exception, stackTrace);
    }

    @Test
    void testPrivateConstructor() {
        assertThrows(IllegalAccessException.class, () -> {
            ExceptionConfig.class.getDeclaredConstructor().newInstance();
        });
    }

    @Test
    void testHolidayExceptionInheritance() {
        ExceptionConfig.HolidayException exception = new ExceptionConfig.HolidayException("Test");
        assertTrue(exception instanceof RuntimeException);
    }
}
