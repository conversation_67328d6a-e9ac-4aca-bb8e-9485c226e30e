//package com.jatismobile.drreceiver.pintarchecker;
//
//import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertThrows;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyInt;
//import static org.mockito.ArgumentMatchers.anyLong;
//import static org.mockito.ArgumentMatchers.anyMap;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.mockito.Mockito.mockStatic;
//import static org.mockito.Mockito.never;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//import java.io.UnsupportedEncodingException;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.stream.Stream;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.junit.jupiter.params.ParameterizedTest;
//import org.junit.jupiter.params.provider.Arguments;
//import org.junit.jupiter.params.provider.MethodSource;
//import org.junit.jupiter.params.provider.ValueSource;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.junit.jupiter.MockitoExtension;
//import com.google.gson.Gson;
//import com.jatismobile.drreceiver.pintarchecker.configuration.ExceptionConfig.HolidayException;
//import com.jatismobile.drreceiver.pintarchecker.configuration.MyGlobalConfig;
//import com.jatismobile.drreceiver.pintarchecker.dao.MongoDao;
//import com.jatismobile.drreceiver.pintarchecker.model.HttpResult;
//import com.jatismobile.drreceiver.pintarchecker.model.SendSmsResponse;
//import com.jatismobile.drreceiver.pintarchecker.model.SendSmsResponse.DataDetails;
//import com.jatismobile.drreceiver.pintarchecker.queue.AgaisQueue;
//import com.jatismobile.drreceiver.pintarchecker.queue.QueueProducer;
//import com.jatismobile.drreceiver.pintarchecker.service.HttpRequestService;
//import com.jatismobile.drreceiver.pintarchecker.utils.ChannelConfigKey;
//import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
//import com.jatismobile.drreceiver.pintarchecker.utils.QueryUtil;
//import com.jatismobile.drreceiver.pintarchecker.utils.ThreadUtil;
//import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;
//
//@ExtendWith(MockitoExtension.class)
//class MessageProcessorTest {
//
//    @Mock
//    private AgaisQueue agaisQueue;
//
//    @Mock
//    private QueueProducer queueProducer;
//
//    @Mock
//    private HttpRequestService httpRequestService;
//
//    @Mock
//    private MyGlobalConfig myGlobalConfig;
//
//    @Mock
//    private MongoDao mongoDao;
//
//    @InjectMocks
//    private MessageProcessor messageProcessor;
//
//    private Map<String, String> configMap;
//    private Map<String, String> providersMap;
//    private Map<String, String> channelConfigMap;
//
//    private String queueMessage = "trxid=pTtOo7byRACtsZ4U7bzd3wV2&messageid=f4cb0296-8521-48ec-a19b-5f7796254f70&msisdn=*************&message=%3C%23%3E+JANGAN+BERIKAN+OTP+kpd+org+lain+termasuk+staf+bank%2C+agar+tidak+digunakan+utk+mengakses+rekening+Anda.+OTP+Livin%3A+********%0A%0ANH805fgwrHd&batchname=Livinsmsotp&clientid=5&divisionid=4265&senderid=BANKMANDIRI&uid=&pwd=&cpName=&uploadby=Livin&approveby=Livin&dateupload=2024-01-03+09%3A47%3A08&dateapprove=2024-01-03+09%3A47%3A08&dateinqueue=&servicetype=OTP&providerid=3&billable=1&jatismessageid=&uploadtype=&smspartition=1&expiry=**************&sender=BANKMANDIRI&channel_mq_name=jns.transmitter.fastsignal.premium.247&priority=9&adddata1=&adddata2=&adddata3=&chargecode=&senderids=%7B%22sender%22%3A%22BANKMANDIRI%22%2C%22maskCodeSettings%22%3A%5B%7B%22key%22%3A%22FASTSIGNAL-REGULAR%22%2C%22data%22%3A%7B%22username%22%3A%22%22%2C%22password%22%3A%22%22%2C%22cpName%22%3A%22%22%2C%22channelIsDirect%22%3A63%2C%22senderID%22%3A%22BANKMANDIRI%22%7D%7D%2C%7B%22key%22%3A%22FASTSIGNAL-PREMIUM%22%2C%22data%22%3A%7B%22username%22%3A%22%22%2C%22password%22%3A%22%22%2C%22cpName%22%3A%22%22%2C%22channelIsDirect%22%3A64%2C%22senderID%22%3A%22BANKMANDIRI%22%7D%7D%5D%7D";
//
//    @BeforeEach
//    void setUp() {
//        configMap = new HashMap<>();
//        configMap.put(ConfigKey.EMAIL, "<EMAIL>");
//        configMap.put(ConfigKey.PASSWORD, "password");
//        configMap.put(ConfigKey.ACTIVEMQ_TXQUE, "queue.tx");
//        configMap.put(ConfigKey.ACTIVEMQ_TXACK, "queue.ack");
//        configMap.put(ConfigKey.RETRY_TIMEOUT, "5000");
//        configMap.put(ConfigKey.MAX_RETRY, "3");
//        configMap.put(ConfigKey.SUCCESS_RECEIVED, "1");
//        configMap.put(ConfigKey.FAILED_SUBMIT, "0");
//        configMap.put(ConfigKey.ERR_EXPIRED, "-1");
//        configMap.put(ConfigKey.PROVIDER_HASHDR, "true");
//        configMap.put(ConfigKey.CHANNEL_NAME, "TEST_CHANNEL");
//        configMap.put(ConfigKey.MONGODB_DATABASE, "database");
//        configMap.put(ConfigKey.MONGODB_TRX_COLLECTION, "collection");
//
//        providersMap = new HashMap<>();
//        providersMap.put("1", "PROVIDER1");
//        providersMap.put("2", "PROVIDER2");
//        providersMap.put("3", "PROVIDER3");
//        providersMap.put("5", "PROVIDER5");
//
//        channelConfigMap = new HashMap<>();
//    }
//
//    @Test
//    void testReceiveQueueMessageWithNullQueueMessage(){
//        messageProcessor.receivedQueueMessage(null);
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer, times(0)).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithEmptyQueueMessage(){
//        messageProcessor.receivedQueueMessage("");
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer, times(0)).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithErrorUnsupportedEncodingException(){
//        try (MockedStatic<QueryUtil> mockedQueryUtil = mockStatic(QueryUtil.class);
//             MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//            mockedQueryUtil.when(() -> QueryUtil.parseQuery(anyString())).thenThrow(UnsupportedEncodingException.class);
//
//            messageProcessor.receivedQueueMessage(queueMessage);
//
//            verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//            mockedAppLogUtil.verify(() -> AppLogUtil.writeErrorLog(anyString(), anyString(), any(Exception.class)), times(1));
//            verify(agaisQueue, times(1)).sendError(anyString(), anyInt(), anyString());
//        }
//    }
//
//    @Test
//    void testReceiveQueueMessageWithErrorUnsupportedEncodingExceptionOnExpiry() throws UnsupportedEncodingException {
//        channelConfigMap.put(ChannelConfigKey.CHANNEL_GROUP_NAME, "FASTSIGNAL-PREMIUM");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        String queueMessage2 = queueMessage.replace("expiry=**************", "expiry=20200103095208");
//        Map<String, String> qHmap = QueryUtil.parseQuery(queueMessage2);
//
//        try (MockedStatic<QueryUtil> mockedQueryUtil = mockStatic(QueryUtil.class);
//             MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//            mockedQueryUtil.when(() -> QueryUtil.queryBuilder(anyMap())).thenThrow(UnsupportedEncodingException.class);
//
//            mockedQueryUtil.when(() -> QueryUtil.parseQuery(anyString())).thenReturn(qHmap);
//
//            messageProcessor.receivedQueueMessage(queueMessage2);
//
//            verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//            mockedAppLogUtil.verify(() -> AppLogUtil.writeErrorLog(anyString(), anyString(), any(Exception.class)), times(1));
//            verify(agaisQueue, times(1)).sendError(anyString(), anyInt(), anyString());
//        }
//    }
//
//    @Test
//    void testReceiveQueueMessageWithErrorException(){
//        try (MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//
//            messageProcessor.receivedQueueMessage("trxid=pTtOo7byRACtsZ4U7bzd3wV2&messageid=f4cb0296-8521-48ec-a19b-5f7796254f70&msisdn=*************&message=%3C%23%3E+JANGAN+BERIKAN+OTP+kpd+org+lain+termasuk+staf+bank%2C+agar+tidak+digunakan+utk+mengakses+rekening+Anda.+OTP+Livin%3A+********%0A%0ANH805fgwrHd&batchname=Livinsmsotp&clientid=5&divisionid=4265&senderid=BANKMANDIRI&uid=&pwd=&cpName=&uploadby=Livin&approveby=Livin&dateupload=2024-01-03+09%3A47%3A08&dateapprove=2024-01-03+09%3A47%3A08&dateinqueue=&servicetype=OTP&providerid=3&billable=1&jatismessageid=&uploadtype=&smspartition=1&expiry=**************&sender=BANKMANDIRI&channel_mq_name=jns.transmitter.fastsignal.premium.247&priority=9&adddata1=&adddata2=&adddata3=&chargecode=&senderids=");
//
//            verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//            mockedAppLogUtil.verify(() -> AppLogUtil.writeErrorLog(anyString(), anyString(), any(Exception.class)));
//            verify(agaisQueue, times(1)).sendError(anyString(), anyInt(), anyString());
//        }
//    }
//
//    @ParameterizedTest
//    @ValueSource(strings = {
//        "", // test success
//        "trxid=pTtOo7byRACtsZ4U7bzd3wV2&messageid=f4cb0296-8521-48ec-a19b-5f7796254f70&msisdn=*************&message=%3C%23%3E+JANGAN+BERIKAN+OTP+kpd+org+lain+termasuk+staf+bank%2C+agar+tidak+digunakan+utk+mengakses+rekening+Anda.+OTP+Livin%3A+********%0A%0ANH805fgwrHd&batchname=Livinsmsotp&clientid=5&divisionid=4265&senderid=BANKMANDIRI&cpName=&uploadby=Livin&approveby=Livin&dateupload=2024-01-03+09%3A47%3A08&dateapprove=2024-01-03+09%3A47%3A08&dateinqueue=&servicetype=OTP&providerid=3&billable=1&jatismessageid=&uploadtype=&smspartition=1&expiry=**************&sender=BANKMANDIRI&channel_mq_name=jns.transmitter.fastsignal.premium.247&priority=9&adddata1=&adddata2=&adddata3=&chargecode=&senderids=%7B%22sender%22%3A%22BANKMANDIRI%22%2C%22maskCodeSettings%22%3A%5B%5D%7D", // test senderids empty list and uid null
//        "trxid=pTtOo7byRACtsZ4U7bzd3wV2&messageid=f4cb0296-8521-48ec-a19b-5f7796254f70&msisdn=*************&message=%3C%23%3E+JANGAN+BERIKAN+OTP+kpd+org+lain+termasuk+staf+bank%2C+agar+tidak+digunakan+utk+mengakses+rekening+Anda.+OTP+Livin%3A+********%0A%0ANH805fgwrHd&batchname=Livinsmsotp&clientid=5&divisionid=4265&senderid=BANKMANDIRI&uid=&pwd=&cpName=&uploadby=Livin&approveby=Livin&dateupload=2024-01-03+09%3A47%3A08&dateapprove=2024-01-03+09%3A47%3A08&dateinqueue=&servicetype=OTP&providerid=3&billable=1&jatismessageid=&uploadtype=&smspartition=1&expiry=**************&sender=BANKMANDIRI&channel_mq_name=jns.transmitter.fastsignal.premium.247&priority=9&adddata1=&adddata2=&adddata3=&chargecode=", // test senderids null
//        "trxid=pTtOo7byRACtsZ4U7bzd3wV2&messageid=f4cb0296-8521-48ec-a19b-5f7796254f70&msisdn=*************&message=%3C%23%3E+JANGAN+BERIKAN+OTP+kpd+org+lain+termasuk+staf+bank%2C+agar+tidak+digunakan+utk+mengakses+rekening+Anda.+OTP+Livin%3A+********%0A%0ANH805fgwrHd&batchname=Livinsmsotp&clientid=5&divisionid=4265&senderid=BANKMANDIRI&uid=&pwd=&cpName=&uploadby=Livin&approveby=Livin&dateupload=2024-01-03+09%3A47%3A08&dateapprove=2024-01-03+09%3A47%3A08&dateinqueue=&servicetype=OTP&providerid=3&billable=1&jatismessageid=&uploadtype=&smspartition=1&expiry=**************&sender=BANKMANDIRI&channel_mq_name=jns.transmitter.fastsignal.premium.247&priority=9&adddata1=&adddata2=&adddata3=&chargecode=&senderids=%7B%22sender%22%3A%22BANKMANDIRI%22%2C%22maskCodeSettings%22%3A%5B%5D%7D" // test senderids empty list
//    })
//    void testReceiveQueueMessage(String message) {
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(new DataDetails(1,"PRV123", "************", "", "Kode Otp Adalah", 2, "20250107T10:00:00", "20250107T10:05:00"));
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(message.isEmpty() ? queueMessage : message);
//
//        verify(mongoDao).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithSenderIdsMaskCodeNull() {
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(new DataDetails(1,"PRV123", "************", "", "Kode Otp Adalah", 2, "20250107T10:00:00", "20250107T10:05:00"));
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        try (MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//            messageProcessor.receivedQueueMessage("trxid=pTtOo7byRACtsZ4U7bzd3wV2&messageid=f4cb0296-8521-48ec-a19b-5f7796254f70&msisdn=*************&message=%3C%23%3E+JANGAN+BERIKAN+OTP+kpd+org+lain+termasuk+staf+bank%2C+agar+tidak+digunakan+utk+mengakses+rekening+Anda.+OTP+Livin%3A+********%0A%0ANH805fgwrHd&batchname=Livinsmsotp&clientid=5&divisionid=4265&senderid=BANKMANDIRI&uid=&pwd=&cpName=&uploadby=Livin&approveby=Livin&dateupload=2024-01-03+09%3A47%3A08&dateapprove=2024-01-03+09%3A47%3A08&dateinqueue=&servicetype=OTP&providerid=3&billable=1&jatismessageid=&uploadtype=&smspartition=1&expiry=**************&sender=BANKMANDIRI&channel_mq_name=jns.transmitter.fastsignal.premium.247&priority=9&adddata1=&adddata2=&adddata3=&chargecode=&senderids=%7B%22sender%22%3A%22BANKMANDIRI%22%7D");
//
//            verify(mongoDao).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//            mockedAppLogUtil.verify(() -> AppLogUtil.writeErrorLog(anyString(), anyString(), any(Exception.class)));
//            verify(queueProducer, times(1)).sendMessage(anyString(), anyString(), anyString(), anyInt());
//            verify(agaisQueue, times(1)).sendError(anyString(), anyInt(), anyString());
//        }
//    }
//
//    @Test
//    void testReceiveQueueMessageWithForwardingQueue(){
//        channelConfigMap.put(ChannelConfigKey.TARGET_MQ_NAME, "forwardingQueue");
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        configMap.put(ConfigKey.ACTIVEMQ_TXQUE, "originQueue");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//
//        messageProcessor.receivedQueueMessage(queueMessage);
//
//        verify(queueProducer, times(1)).sendMessage(anyString(), eq("forwardingQueue"), anyString(), eq(9));
//        verify(httpRequestService, never()).sendSms(anyString(), anyString(), anyString(), anyString(), anyString());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithForwardingQueuePriorityNull(){
//        channelConfigMap.put(ChannelConfigKey.TARGET_MQ_NAME, "forwardingQueue");
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        configMap.put(ConfigKey.ACTIVEMQ_TXQUE, "originQueue");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//
//        String queueMessage2 = queueMessage.replace("&priority=9", "");
//
//        messageProcessor.receivedQueueMessage(queueMessage2);
//
//        verify(queueProducer, times(1)).sendMessage(anyString(), eq("forwardingQueue"), anyString(), eq(4));
//        verify(httpRequestService, never()).sendSms(anyString(), anyString(), anyString(), anyString(), anyString());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithForwardingQueuePriorityError(){
//        try (MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//            channelConfigMap.put(ChannelConfigKey.TARGET_MQ_NAME, "forwardingQueue");
//            when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//            configMap.put(ConfigKey.ACTIVEMQ_TXQUE, "originQueue");
//            when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//
//            String queueMessage2 = queueMessage.replace("priority=9", "priority=XXX");
//
//            messageProcessor.receivedQueueMessage(queueMessage2);
//
//            mockedAppLogUtil.verify(() -> AppLogUtil.writeErrorLog(anyString(), anyString(), any(NumberFormatException.class)));
//            verify(queueProducer, times(1)).sendMessage(anyString(), eq("forwardingQueue"), anyString(), eq(4));
//            verify(httpRequestService, never()).sendSms(anyString(), anyString(), anyString(), anyString(), anyString());
//        }
//    }
//
//    @Test
//    void testReceiveQueueMessageWithForwardingQueueEmptyTarget(){
//        try (MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//            channelConfigMap.put(ChannelConfigKey.TARGET_MQ_NAME, "");
//            when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//            configMap.put(ConfigKey.ACTIVEMQ_TXQUE, "originQueue");
//            when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//
//            messageProcessor.receivedQueueMessage(queueMessage);
//
//            verify(queueProducer, times(0)).sendMessage(anyString(), eq("forwardingQueue"), anyString(), eq(4));
//        }
//    }
//
//    @Test
//    void testReceiveQueueMessageWithDataSenderOnQueue(){
//        channelConfigMap.put(ChannelConfigKey.CHANNEL_GROUP_NAME, "FASTSIGNAL-PREMIUM");
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        String queueMessage2 = queueMessage.replace("%22username%22%3A%22%22%2C%22password%22%3A%22%22%2C",
//        "%22username%22%3A%22EmailJson%40jatis.com%22%2C%22password%22%3A%22XXXXJson%22%2C");
//
//        messageProcessor.receivedQueueMessage(queueMessage2);
//
//        verify(httpRequestService).sendSms(anyString(), eq("<EMAIL>"), eq("XXXXJson"), anyString(), anyString());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithExpiryMessage(){
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//
//        String queueMessage2 = queueMessage.replace("expiry=**************", "expiry=20200103095208");
//
//        messageProcessor.receivedQueueMessage(queueMessage2);
//
//        verify(httpRequestService, never()).sendSms(anyString(), anyString(), anyString(), anyString(), anyString());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithExpiryNull(){
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//
//        String queueMessage2 = queueMessage.replace("&expiry=**************", "");
//
//        messageProcessor.receivedQueueMessage(queueMessage2);
//
//        verify(httpRequestService, times(1)).sendSms(anyString(), anyString(), anyString(), anyString(), anyString());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithExpiryEmpty(){
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//
//        String queueMessage2 = queueMessage.replace("&expiry=**************", "&expiry=");
//
//        messageProcessor.receivedQueueMessage(queueMessage2);
//
//        verify(httpRequestService, times(1)).sendSms(anyString(), anyString(), anyString(), anyString(), anyString());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithRetry(){
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(false);
//        httpResult.setCanRetry(true);
//        httpResult.setHasError(true);
//        httpResult.setStatusCode(500);
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage);
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer, times(1)).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageFailedHitButCantRetry(){
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//
//        channelConfigMap.put(ChannelConfigKey.CHANNEL_GROUP_NAME, "FASTSIGNAL-PREMIUM");
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(false);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(true);
//        httpResult.setStatusCode(500);
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage);
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer, times(1)).sendMessage(anyString(), anyString(), anyString(), anyInt());
//        verify(agaisQueue, times(1)).sendError(anyString(), anyInt(), anyString());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithRetryQueue2(){
//        configMap.put(ConfigKey.MAX_RETRY, "3");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(false);
//        httpResult.setCanRetry(true);
//        httpResult.setHasError(true);
//        httpResult.setStatusCode(500);
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage + "&retry=1");
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer, times(1)).sendMessage(anyString(), anyString(), eq(queueMessage + "&retry=2"), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageWithRetryMaxRetry(){
//        configMap.put(ConfigKey.MAX_RETRY, "3");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(false);
//        httpResult.setCanRetry(true);
//        httpResult.setHasError(true);
//        httpResult.setStatusCode(500);
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage + "&retry=2");
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer, times(0)).sendMessage(anyString(), anyString(), eq(queueMessage + "&retry=3"), anyInt());
//        verify(queueProducer, times(1)).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageSuccessWithErrorFromJsonOnSendAck(){
//        try (MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//            when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//            when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//            when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//            HttpResult httpResult = new HttpResult();
//            httpResult.setSuccess(true);
//            httpResult.setCanRetry(false);
//            httpResult.setHasError(false);
//            httpResult.setStatusCode(200);
//
//            httpResult.setResponse("{");
//
//            when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//                .thenReturn(httpResult);
//
//            messageProcessor.receivedQueueMessage(queueMessage);
//
//            verify(mongoDao, times(0)).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//            mockedAppLogUtil.verify(() -> AppLogUtil.writeErrorLog(anyString(), anyString(), any(Exception.class)), times(2));
//            verify(queueProducer, times(1)).sendMessage(anyString(), anyString(), anyString(), anyInt());
//        }
//    }
//
//    @Test
//    void testReceiveQueueMessageSuccessWithTrueIsHasErrorOnSendAck(){
//        try (MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class)) {
//            when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//            when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//            when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//            HttpResult httpResult = new HttpResult();
//            httpResult.setSuccess(true);
//            httpResult.setCanRetry(false);
//            httpResult.setHasError(true);
//            httpResult.setStatusCode(200);
//
//            httpResult.setResponse("{}");
//
//            when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//                .thenReturn(httpResult);
//
//            messageProcessor.receivedQueueMessage(queueMessage);
//
//            verify(mongoDao, times(0)).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//            verify(queueProducer, times(1)).sendMessage(anyString(), anyString(), anyString(), anyInt());
//        }
//    }
//
//    @Test
//    void testReceiveQueueMessageSuccessWithNullData(){
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(null);
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage);
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @ParameterizedTest
//    @MethodSource("originTestCases")
//    void testReceiveQueueMessageWithOrigin(String originList, String queueMessageOrigin) {
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//
//        if (originList != null) {
//            channelConfigMap.put(ChannelConfigKey.ORIGIN_LIST, originList);
//        }
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(null);
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        String testQueueMessage = queueMessageOrigin != null ?
//            queueMessage + "&origin_mq_name=" + queueMessageOrigin :
//            queueMessage;
//
//        messageProcessor.receivedQueueMessage(testQueueMessage);
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    private static Stream<Arguments> originTestCases() {
//        return Stream.of(
//            Arguments.of("origin.mq.name", "origin.mq.name"),           // Valid origin
//            Arguments.of("origin.mq.name", null),                       // Queue origin null
//            Arguments.of(null, "origin.mq.name"),                       // Origin map null
//            Arguments.of("", ""),                                       // Empty origin
//            Arguments.of("", null),                                     // Empty map key null
//            Arguments.of(null, ""),                                     // Empty key map null
//            Arguments.of("", "origin.mq.name"),                        // Map empty
//            Arguments.of("origin.mq.name", "other.mq.name")            // Origin not contain
//        );
//    }
//    @Test
//    void testReceiveQueueMessageSuccessWithWithProviderHasDR(){
//        configMap.put(ConfigKey.PROVIDER_HASHDR, "true");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(null);
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage);
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageSuccessWithProviderHasDRFalse(){
//        configMap.put(ConfigKey.PROVIDER_HASHDR, "false");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(null);
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage);
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageSuccessWithNullRefferenceId(){
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(new DataDetails(1,null, "************", "", "Kode Otp Adalah", 2, "20250107T10:00:00", "20250107T10:05:00"));
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage);
//
//        verify(mongoDao, never()).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageSuccessWithSleepAfterSendSms(){
//        configMap.put(ConfigKey.SLEEP_AFTER_SEND, "2000");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(new DataDetails(1,"PRV123", "************", "", "Kode Otp Adalah", 2, "20250107T10:00:00", "20250107T10:05:00"));
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        messageProcessor.receivedQueueMessage(queueMessage);
//
//        verify(mongoDao).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//        verify(queueProducer).sendMessage(anyString(), anyString(), anyString(), anyInt());
//    }
//
//    @Test
//    void testReceiveQueueMessageSuccessWithSleepAfterSendSmsWithInteruptedException(){
//        configMap.put(ConfigKey.SLEEP_AFTER_SEND, "2000");
//        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);
//        when(myGlobalConfig.getProvidersHashMap()).thenReturn(providersMap);
//        when(myGlobalConfig.getChannelConfigMap()).thenReturn(channelConfigMap);
//
//        HttpResult httpResult = new HttpResult();
//        httpResult.setSuccess(true);
//        httpResult.setCanRetry(false);
//        httpResult.setHasError(false);
//        httpResult.setStatusCode(200);
//
//        SendSmsResponse response = new SendSmsResponse();
//        response.setStatus(200);
//        response.setCode(0);
//        response.setMessage("Success");
//        response.setData(new DataDetails(1,"PRV123", "************", "", "Kode Otp Adalah", 2, "20250107T10:00:00", "20250107T10:05:00"));
//
//        httpResult.setResponse(new Gson().toJson(response));
//
//        when(httpRequestService.sendSms(anyString(), anyString(), anyString(), anyString(), anyString()))
//            .thenReturn(httpResult);
//
//        try (MockedStatic<AppLogUtil> mockedAppLogUtil = mockStatic(AppLogUtil.class);
//             MockedStatic<ThreadUtil> mockedThreadUtil = mockStatic(ThreadUtil.class)) {
//            mockedThreadUtil.when(() -> ThreadUtil.sleep(anyLong())).thenThrow(new InterruptedException());
//            messageProcessor.receivedQueueMessage(queueMessage);
//
//            verify(mongoDao).insertProviderTrxId(anyString(), anyString(), anyString(), anyString(), any(Date.class));
//            verify(queueProducer).sendMessage(anyString(), anyString(), anyString(), anyInt());
//            mockedAppLogUtil.verify(() -> AppLogUtil.writeErrorLog(anyString(), anyString(), any(Exception.class)), times(1));
//        }
//    }
//
//    @Test
//    void testHolidayCheckThrowsException() {
//        when(myGlobalConfig.isHoliday()).thenReturn(true);
//
//        HolidayException exception = assertThrows(
//            HolidayException.class,
//            () -> messageProcessor.onApplicationStart()
//        );
//
//        assertEquals("Today is holiday. Aborting startup.", exception.getMessage());
//        verify(httpRequestService, never()).sendSms(anyString(), anyString(), anyString(), anyString(), anyString());
//    }
//
//    @Test
//    void testNonHolidayAllowsProcessing() {
//        when(myGlobalConfig.isHoliday()).thenReturn(false);
//
//        assertDoesNotThrow(() -> messageProcessor.onApplicationStart());
//    }
//}