package com.jatismobile.drreceiver.pintarchecker.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import org.junit.jupiter.api.Test;

class MaskCodeSettingsTest {

    @Test
    void testMaskCodeSettingsConstructorAndGetters() {
        TransmitterSetting transmitterSetting = new TransmitterSetting();
        MaskCodeSettings maskCodeSettings = new MaskCodeSettings("testKey", transmitterSetting);
        
        assertNotNull(maskCodeSettings);
        assertEquals("testKey", maskCodeSettings.getKey());
        assertEquals(transmitterSetting, maskCodeSettings.getData());
    }

    @Test
    void testMaskCodeSettingsSetters() {
        MaskCodeSettings maskCodeSettings = new MaskCodeSettings(null, null);
        TransmitterSetting transmitterSetting = new TransmitterSetting();
        
        maskCodeSettings.setKey("newKey");
        maskCodeSettings.setData(transmitterSetting);
        
        assertEquals("newKey", maskCodeSettings.getKey());
        assertEquals(transmitterSetting, maskCodeSettings.getData());
    }

    @Test
    void testMaskCodeSettingsWithNullValues() {
        MaskCodeSettings maskCodeSettings = new MaskCodeSettings(null, null);
        
        assertNull(maskCodeSettings.getKey());
        assertNull(maskCodeSettings.getData());
    }
}
