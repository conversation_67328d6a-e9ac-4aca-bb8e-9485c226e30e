package com.jatismobile.drreceiver.pintarchecker.configuration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;

import javax.jms.ConnectionFactory;

import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.activemq.RedeliveryPolicy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.autoconfigure.jms.DefaultJmsListenerContainerFactoryConfigurer;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.connection.CachingConnectionFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

@ExtendWith(MockitoExtension.class)
class QueueConfigTest {

    @Mock
    private MyGlobalConfig myGlobalConfig;

    @InjectMocks
    private QueueConfig queueConfig;

    private Map<String, String> configMap;

    @BeforeEach
    void setUp() {
        configMap = new HashMap<>();
        configMap.put(ConfigKey.ACTIVEMQ_HOST, "tcp://localhost:61616");
        configMap.put(ConfigKey.ACTIVEMQ_USERNAME, "admin");
        configMap.put(ConfigKey.ACTIVEMQ_PASSWORD, "admin");
        configMap.put(ConfigKey.QUEUE_PERSISTENCY, "true");
        
        ReflectionTestUtils.setField(queueConfig, "appName", "TestApp");
        ReflectionTestUtils.setField(queueConfig, "connectionCache", 10);
        ReflectionTestUtils.setField(queueConfig, "concurrency", "3-5");
        ReflectionTestUtils.setField(queueConfig, "maxMessagePerTask", 1);
        ReflectionTestUtils.setField(queueConfig, "receiveTimeout", 1000L);
    }

    @Test
    void testConnectionFactory() {
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        ConnectionFactory factory = queueConfig.connectionFactory();
        
        assertTrue(factory instanceof ActiveMQConnectionFactory);
        ActiveMQConnectionFactory activeMQFactory = (ActiveMQConnectionFactory) factory;
        
        assertEquals("tcp://localhost:61616", activeMQFactory.getBrokerURL());
        assertEquals("admin", activeMQFactory.getUserName());
        assertEquals("admin", activeMQFactory.getPassword());
        assertTrue(activeMQFactory.isUseAsyncSend());
    }

    @Test
    void testRedeliveryPolicy() {
        RedeliveryPolicy policy = queueConfig.redeliveryPolicy();
        
        assertEquals(10000, policy.getInitialRedeliveryDelay());
        assertEquals(3, policy.getMaximumRedeliveries());
        assertTrue(policy.isUseExponentialBackOff());
        assertEquals(2, policy.getBackOffMultiplier());
    }

    @Test
    void testCachingConnectionFactory() {
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        CachingConnectionFactory factory = (CachingConnectionFactory) queueConfig.cachingConnectionFactory();
        
        assertEquals(10, factory.getSessionCacheSize());
        assertTrue(factory.isCacheProducers());
    }

    @Test
    void testJmsTemplate() {
        when(myGlobalConfig.getLookupConfigMap()).thenReturn(configMap);

        JmsTemplate template = queueConfig.jmsTemplate();
        
        assertTrue(template.isExplicitQosEnabled());
        assertNotNull(template.getConnectionFactory());
        assertTrue(template.getConnectionFactory() instanceof CachingConnectionFactory);
    }
    
    @Test
    void testStartUp(){
        try (MockedStatic<AppLogUtil> mockAppLogUtil = Mockito.mockStatic(AppLogUtil.class)) {
            
            queueConfig.onStartup(null);
            
            mockAppLogUtil.verify(() -> AppLogUtil.writeInfoLog(null, "[TestApp] | QueueConfig is ready."), times(1));
        }
    }
    
    @Test
    void testJmsListenerContainerFactory() {        
        DefaultJmsListenerContainerFactoryConfigurer configurer = mock(DefaultJmsListenerContainerFactoryConfigurer.class);
        ConnectionFactory connectionFactory = mock(ConnectionFactory.class);
        
        DefaultJmsListenerContainerFactory factory = queueConfig.jmsListenerContainerFactory(configurer, connectionFactory);
        
        verify(configurer).configure(factory, connectionFactory);
    }

    @Test
    void testJmsListenerContainerFactoryCustomValues() {        
        ReflectionTestUtils.setField(queueConfig, "concurrency", "1-3");
        ReflectionTestUtils.setField(queueConfig, "maxMessagePerTask", 5);
        ReflectionTestUtils.setField(queueConfig, "receiveTimeout", 2000L);
        
        DefaultJmsListenerContainerFactoryConfigurer configurer = mock(DefaultJmsListenerContainerFactoryConfigurer.class);
        ConnectionFactory connectionFactory = mock(ConnectionFactory.class);
        
        DefaultJmsListenerContainerFactory factory = queueConfig.jmsListenerContainerFactory(configurer, connectionFactory);
        
        verify(configurer).configure(factory, connectionFactory);
    }
}