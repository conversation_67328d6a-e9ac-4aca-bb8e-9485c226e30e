package com.jatismobile.drreceiver.pintarchecker.queue;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import com.jatismobile.drreceiver.pintarchecker.utils.log.AmqLogUtil;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;

@Component
public class QueueProducer {
    @Value("${custom.app.name}")
    private String appName;
    
    private final JmsTemplate jmsTemplate;

    @Autowired
    public QueueProducer(JmsTemplate jmsTemplate){
        this.jmsTemplate = jmsTemplate;
    }

    public void sendMessage(String guid, String queueName, String message, Integer priority){
        try {
            AmqLogUtil.writeInfoLog(guid, "[QueueProducer][sendQueue] Sending message to " + queueName + " : " + message);
            jmsTemplate.setPriority(priority);
            jmsTemplate.convertAndSend(queueName, message);
        }catch (Exception ex){
            AmqLogUtil.writeErrorLog(guid, "[QueueProducer][sendQueue] Error sending message to queue caused by : ", ex);
            throw ex;
        }
    }

    @EventListener
    public void onStartup(ApplicationReadyEvent event) {
        AppLogUtil.writeInfoLog(null, "[" + appName + "] | " + getClass().getSimpleName() + " is ready.");
    }
}
