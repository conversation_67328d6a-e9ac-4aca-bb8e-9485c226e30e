package com.jatismobile.drreceiver.pintarchecker.configuration;

import com.jatismobile.drreceiver.pintarchecker.dao.RerouteMappingDao;
import com.jatismobile.drreceiver.pintarchecker.entity.mysql.appsconfig.JnsConfig;
import com.jatismobile.drreceiver.pintarchecker.repository.mysql.appsconfig.JnsConfigRepository;
import com.jatismobile.drreceiver.pintarchecker.utils.ConfigKey;
import com.jatismobile.drreceiver.pintarchecker.utils.log.AppLogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class MyGlobalConfig {
    @Value("${custom.app.name}")
    private String appName;

    private String urlMySql;
    private String usernameDbBroadcast;
    private String passwordDbBroadcast;
    private String activemqTxque;

    private static final String DATAS_STR = "datas";

    private final JnsConfigRepository jnsConfigRepository;
    private final ApplicationProperties applicationProperties;

    private Map<String, String> rerouteHashMap = new HashMap<>();
    private Map<String, String> lookupConfigMap = new HashMap<>();

    @Autowired
    public MyGlobalConfig(JnsConfigRepository jnsConfigRepository,
            ApplicationProperties applicationProperties) {
        this.jnsConfigRepository = jnsConfigRepository;
        this.applicationProperties = applicationProperties;
        this.initConfig();
    }

    public void initConfig() {
        initLookupConfig();
        initRerouteConfig();
    }

    public void initLookupConfig() {
        Iterable<JnsConfig> jnsConfigIterable = jnsConfigRepository
                .findByLookupType(applicationProperties.getLookupType());
        jnsConfigIterable.forEach(jnsConfig -> lookupConfigMap.put(String.valueOf(jnsConfig.getLookupName()),
                jnsConfig.getLookupValue()));

        AppLogUtil.writeInfoLog(null, "[MyGlobalConfig] Success get config jns config map : "
                + lookupConfigMap.size() + " " + DATAS_STR);

        urlMySql = "jdbc:mysql://" + lookupConfigMap.get(ConfigKey.DB_HOST) + ":"
                + lookupConfigMap.get(ConfigKey.DB_PORT) + "/"
                + lookupConfigMap.get(ConfigKey.DB_NAME)
                + (lookupConfigMap.get(ConfigKey.DB_NAME).contains("?") ? "&" : "?") + "serverTimezone="
                + applicationProperties.getServerTimezone();
        usernameDbBroadcast = lookupConfigMap.get(ConfigKey.DB_USER);
        passwordDbBroadcast = lookupConfigMap.get(ConfigKey.DB_PASSWORD);
        activemqTxque = lookupConfigMap.get(ConfigKey.ACTIVEMQ_TXQUE).trim();
    }



    public void initRerouteConfig() {
        rerouteHashMap = RerouteMappingDao.findAll(urlMySql, usernameDbBroadcast, passwordDbBroadcast,
                lookupConfigMap.get(ConfigKey.DB_TABLE));
        AppLogUtil.writeInfoLog(null, "[MyGlobalConfig] Success get reroute channel map : "
                + rerouteHashMap.size() + " " + DATAS_STR);
    }



    public Map<String, String> getRerouteHashMap() {
        return rerouteHashMap;
    }

    public void setRerouteHashMap(Map<String, String> rerouteHashMap) {
        this.rerouteHashMap = rerouteHashMap;
    }

    public void setLookupConfigMap(Map<String, String> lookupConfigMap) {
        this.lookupConfigMap = lookupConfigMap;
    }

    public Map<String, String> getLookupConfigMap() {
        return lookupConfigMap;
    }

    @EventListener
    public void onStartup(ApplicationReadyEvent event) {
        AppLogUtil.writeInfoLog(null,
                "[" + appName + "] | " + getClass().getSimpleName().split("\\$")[0] + " is ready.");
    }
}
